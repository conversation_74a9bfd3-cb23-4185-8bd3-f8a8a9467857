import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace ReportApi {
  export interface Report {
    [key: string]: any;

    // 报告ID
    id: number;

    // 客户ID
    clientId: number;

    // 公司ID
    corpId: number;

    // 报告名称
    name: string;

    // 报告模板
    templateId: number;

    // 报告状态
    stage: number;

    // 报告进度
    progress: number;

    // 概述审核状态
    summaryAuditTag: number;

    // 详情审核状态
    detailAuditTag: number;
  }

  export interface ReportInfo {
    taxId: string;
    stage: number;
    progress: number;
  }

  /* 财务管理建议 */
  export interface AnaItem {
    listIndex: number;
    title: string;
    description: string;
    level: string;
  }

  /* 税负数据 */
  export interface TaxBurden {
    listIndex: number;
    year: number;
    incomeTax: {
      assetAdjustments1: number;
      assetAdjustments2: number;
      assetAdjustments3: number;
      deductionAdjustments1: number;
      deductionAdjustments2: number;
      deductionAdjustments3: number;
      deductionAdjustments4: number;
      deductionAdjustments5: number;
      deductionAdjustments6: number;
      deductionAdjustments7: number;
      deductionAdjustments8: number;
      deductionAdjustments9: number;
      deductionAdjustments10: number;
      deductionAdjustments11: number;
      deductionAdjustments12: number;
      deductionAdjustments13: number;
      deductionAdjustments14: number;
      deductionAdjustments15: number;
      deductionAdjustments16: number;
      deductionAdjustments17: number;
      deductionAdjustments18: number;
      deductionAdjustments19: number;
      incomeDecrease1: number;
      incomeDecrease2: number;
      incomeDecrease3: number;
      incomeDecrease4: number;
      incomeDecrease5: number;
      incomeDecrease6: number;
      incomeIncrease1: number;
      incomeIncrease2: number;
      incomeIncrease3: number;
      specialAdjustments1: number;
      specialAdjustments2: number;
    };
    vat: {
      inputTax1: number;
      inputTax2: number;
      inputTax3: number;
      inputTax4: number;
      outputTax1: number;
      outputTax2: number;
      outputTax3: number;
      outputTax4: number;
      outputTax5: number;
      outputTax6: number;
      taxRateOthers1: number;
      taxRateOthers2: number;
      taxRateOthers3: number;
      taxRateOthers4: number;
    };
    otherTax: {
      constructionTax1: number;
      constructionTax2: number;
      consumptionTax1: number;
      consumptionTax2: number;
      landUseTax1: number;
      personalIncomeTax1: number;
      personalIncomeTax2: number;
      personalIncomeTax3: number;
      personalIncomeTax4: number;
      propertyTax1: number;
      propertyTax2: number;
      propertyTax3: number;
      resourceTax1: number;
      stampDuty1: number;
      stampDuty2: number;
      stampDuty3: number;
      stampDuty4: number;
    };
  }

  export interface CorpInfo {
    taxId: string; // 社会信用代码
    address: string; // 注册地址
    eastabDate: string; // 登记日期
    name: string; // 企业名称
    state: number; // 当前状态 (-1:未知、0:在营、1:开业、2:在册、3:注销)
    registerCapital: string; // 注册资本
    paidInCapital: string; // 实收资本
    sector: string; // 所属行业
    opScope: string; // 经营范围
    regOrgName: string; // 主管税务机关
    frname: string; // 法人姓名
    capTypeName: string; // 登记注册类型
    creditRating: string; // 纳税信用等级
    isExportCompany: number; // 是否出口企业 (0:否、1:是)
    isHighTech: number; // 是否高新技术企业 (0:否、1:是)
    isSmallMicro: number; // 是否小微企业 (0:否、1:是)
    frAge: string; // 法人年龄
    frIdCard: string; // 法人证件号码
    frIdType: string; // 法人证件类型
    frOrigin: string; // 法人籍贯
    taxpayerType: string; // 纳税人类型
  }

  /* 股东交叉持股信息 */
  export interface CrossShareholdingInfo {
    shareholderName: string;
    shareholdingRatio: number;
  }

  /* 交叉公司信息 */
  export interface CrossCompanyInfo {
    companyName: string;
    position?: string;
    [key: string]: any;
  }

  /* 基本信息 */
  export interface BasicInfo {
    status: number;
    industry: string;
    taxId: string;
    corpName: string;
    legalPerson: {
      age: number;
      idNumber: string;
      idType: string;
      name: string;
      origin: string;
    };
    taxAuthority: string;
    taxpayerType: string;
    businessScope: string;
    paidInCapital: number;
    taxCreditRating: string;
    registrationDate: string;
    registrationType: string;
    registeredAddress: string;
    registeredCapital: number;
    isExportEnterprise: number;
    shareholderAnalysis: {
      crossExecutivesCompanies: CrossCompanyInfo[];
      crossLegalRepresentativeCompanies: CrossCompanyInfo[];
      crossShareholdingCompanies: CrossShareholdingInfo[];
    };
    isHighTechEnterprise: number;
    isSmallMicroEnterprise: number;
  }

  /* 增值税项目 */
  export interface VatItem {
    year: number;
    index: number;
    inputTax: {
      abnormalLoss: number;
      nonCompliantInvoiceList: {
        fraudulentInvoices: any[];
        mismatchedCostInventory: any[];
        mismatchedItems: any[];
        overduePayables: any[];
        unusedInventory: any[];
      };
      nonCompliantInvoices: number;
      nonDeductibleInputs: number;
      nonDeductibleList: any[];
      specialItemDeductions: number;
    };
    otherVat: {
      energyBasedEstimate: number;
      incorrectSimplifiedMethod: number;
      lowTaxBurden: number;
      taxExemptInvoices: any[];
      understatedHighRateItems: number;
      zeroRateInvoices: any[];
    };
    outputTax: {
      creditNoteList: any[];
      deemedInterestSales: number;
      incorrectDiscounts: number;
      incorrectTaxRate: number;
      unrecordedAdvanceReceipts: number;
      unrecordedDeemedSales: number;
      unrecordedScrapIncome: number;
      unrecordedSurcharges: number;
    };
  }

  /* 印花税项目 */
  export interface StampDutyItem {
    year: number;
    index: number;
    incorrectTaxCategory: number;
    unrecordedHiddenIncome: number;
    unrecordedEquityChanges: number;
    unrecordedCapitalIncrease: number;
  }

  /* 土地使用税项目 */
  export interface LandUseTaxItem {
    year: number;
    index: number;
    incorrectLandArea: number;
    incorrectLandGradeRate: number;
  }

  /* 房产税项目 */
  export interface PropertyTaxItem {
    year: number;
    index: number;
    calculationError: number;
    landValueOmission: number;
    unpaidRelatedPartyRent: number;
  }

  /* 消费税项目 */
  export interface ConsumptionTaxItem {
    year: number;
    index: number;
    unrecordedGifts: number;
    incorrectTaxRate: number;
  }

  /* 企业所得税项目 */
  export interface CorporateIncomeTaxItem {
    year: number;
    index: number;
    assetAdjustments: {
      assetLossAdjustment: number;
      depreciationAdjustment: number;
      nonDeductibleReserves: number;
    };
    incomeDeductions: {
      nonTaxableGovIncome: number;
      priorPeriodOverstatedIncome: number;
      priorPeriodReturns: number;
      resourceUtilizationDeduction: number;
      taxExemptBondInterest: number;
      taxExemptDividends: number;
    };
    expenseDeductions: {
      crossPeriodCosts: number;
      rdDeduction: number;
      smallScaleInvoiceAdjustment: number;
      smallScaleInvoices: any[];
    };
    incomeAdjustments: {
      deemedInterestIncome: number;
      deemedSalesIncome: number;
      unrecordedAdvanceReceipts: number;
      unrecordedRentInterestIncome: number;
      unrecordedScrapIncome: number;
    };
    expenseAdjustments: {
      conclusion: string;
      deemedSalesCost: number;
      disabledStaffDeduction: number;
      ecoEquipmentDeduction: number;
      excessAdvertising: number;
      excessCommission: number;
      excessDonations: number;
      excessEntertainment: number;
      excessTrainingFees: number;
      excessUnionFees: number;
      excessWelfare: number;
      industryMaterialAnalysis: any[];
      inventoryValuationAdjustment: number;
      irrelevantExpenses: number;
      mismatchedCosts: number;
      negativeInventory: number;
      nonBusinessSponsorship: number;
      nonCharitableDonations: number;
      nonDeductibleInterest: number;
      nonDeductiblePenalties: number;
      nonDeductibleSalaries: number;
      nonTaxableExpenses: number;
      overstatedCosts: number;
      personalSocialInsurance: number;
      unapprovedReserves: number;
      uninvoicedCosts: number;
      unrecordedCosts: number;
      ventureInvestmentDeduction: number;
    };
    specialAdjustments: {
      relocationDeferral: number;
      reorganizationDeferral: number;
    };
  }

  /* 教育费附加项目 */
  export interface EducationSurchargeItem {
    year: number;
    index: number;
    estimatedUnderpayment: number;
    unrecordedExportCredit: number;
    unrecordedHiddenIncome: number;
  }

  /* 个人所得税项目 */
  export interface IndividualIncomeTaxItem {
    year: number;
    specialNotes: number;
    deemedDividends: number;
    deemedGiftSales: number;
    unwithheldLaborIncome: number;
    splitExecutiveSalaries: number;
  }

  /* 社会保险金项目 */
  export interface SocialInsuranceItem {
    year: number;
    insufficientSocialInsurance: number;
  }

  /* 城市维护建设税项目 */
  export interface UrbanMaintenanceTaxItem {
    year: number;
    index: number;
    estimatedUnderpayment: number;
    unrecordedExportCredit: number;
    unrecordedHiddenIncome: number;
  }

  /* 政策优惠 */
  export interface PolicyBenefits {
    ipoSubsidy: string;
    rentSubsidy: string;
    talentSubsidy: string;
    otherSubsidies: string;
    industrySubsidy: string;
    equipmentSubsidy: string;
  }

  /* 税务分析 */
  export interface TaxAnalysis {
    vat: VatItem[];
    stampDuty: StampDutyItem[];
    landUseTax: LandUseTaxItem[];
    propertyTax: PropertyTaxItem[];
    resourceTax: any[];
    consumptionTax: ConsumptionTaxItem[];
    corporateIncomeTax: CorporateIncomeTaxItem[];
    educationSurcharge: EducationSurchargeItem[];
    individualIncomeTax: IndividualIncomeTaxItem[];
    landAppreciationTax: any[];
    urbanMaintenanceTax: UrbanMaintenanceTaxItem[];
    socialInsurance: SocialInsuranceItem[];
  }

  /* 发票分析 */
  export interface InvoiceAnalysis {
    salesInvoice: {
      abnormalAccounts: any[];
      invoiceRiskStats?: Array<{
        corpName: string;
        risks?: string;
        taxId: string;
        totalAmount: string;
      }>;
      riskCompanies: any[];
    };
    purchaseInvoice: {
      abnormalAccounts: any[];
      invoiceRiskStats?: Array<{
        corpName: string;
        risks?: string;
        taxId: string;
        totalAmount: string;
      }>;
      nonCompliantInvoiceList?: {
        fraudulentInvoices: any[];
      };
      riskCompanies: any[];
    };
    largeConsultingInvoices: any[];
    retailSupermarketInvoices: any[];
    purchaseTotalAmount?: number;
    salesTotalAmount?: number;
    isPurchaseGTSalesTotal?: boolean;
    purchaseTax?: number;
    salesTax?: number;
    isSalesMLTPurchaseTax?: boolean;
    isOnlySales?: boolean;
    taxExemptComparisons?: Array<{
      diff: number;
      purchase: number;
      sale: number;
      year: number;
    }>;
    salesVsDeclares?: Array<{
      decl: number;
      diff: number;
      sale: number;
      year: number;
    }>;
    zeroRateInvoices?: any[];
    taxExemptInvoices?: any[];
  }

  export interface ProductItem {
    name: string;
    unit: string;
    amount: number;
    avgPrice: number;
    quantity: number;
    percentage: number;
    index?: number;
  }

  /* 规划分析 */
  export interface PlanningAnalysis {
    corporateStructure: string;
    investmentAnalysis: string;
  }

  /* 产品分析 */
  export interface ProductAnalysis {
    conclusion: string;
    mainSalesProducts: ProductItem[];
    mainPurchaseProducts: ProductItem[];
  }

  /* 财务分析 */
  export interface FinancialAnalysis {
    salesExpenses: any;
    financialExpenses: any;
    inventoryAnalysis: any;
    efficiencyAnalysis: any;
    managementExpenses: any;
    receivablesAnalysis: any;
  }

  /* 税负数据接口响应类型 */
  export interface TaxBurdenItem {
    id: number;
    createdAt: string;
    updatedAt: string;
    corpId: number;
    year: number;
    property: number; // 资产
    revenue: number; // 收入
    cost: number; // 成本
    liabilities: number; // 负债
    grossProfit: number; // 毛利润
    netProfit: number; // 净利润
    retainedEarnings: number; // 未分配利润
    totalTax: number; // 总税额
    vat: number; // 增值税
    incomeTax: number; // 所得税
    stampDuty: number; // 印花税
    landAppreciationTax: number; // 土地增值税
    propertyTax: number; // 房产税
    urbanLandUseTax: number; // 城镇土地使用税
    culturalConstructionFee: number; // 文化事业建设费
    disabilityEmploymentFund: number; // 残保金
    grossProfitMargin: number; // 毛利率(%)
    netProfitMargin: number; // 净利率(%)
    liabilitiesRatio: number; // 负债率(%)
    totalTaxBurdenRate: number; // 总税负率(%)
    vatBurdenRate: number; // 增值税税负率(%)
    industryVatBurdenRate: number; // 行业平均增值税税负率(%)
    incomeTaxBurdenRate: number; // 所得税税负率(%)
    industryIncomeTaxBurdenRate: number; // 行业平均所得税税负率(%)
  }

  /* 新的报告结果数据接口响应类型 */
  export interface NewReportResultData {
    version: string;
    basicInfo: BasicInfo;
    taxAnalysis: TaxAnalysis;
    policyBenefits: PolicyBenefits;
    invoiceAnalysis: InvoiceAnalysis;
    productAnalysis: ProductAnalysis;
    planningAnalysis: PlanningAnalysis;
    financialAnalysis: FinancialAnalysis;
  }

  /* 类型别名，使用新的数据结构作为默认 */
  export type ReportResultData = NewReportResultData;
}

/**
 * 获取报告列表
 */
async function getReportList(params: Recordable<any>) {
  return requestClient.get<{ records: any; total: number }>('/report/list', {
    params,
  });
}

/**
 * 新增报告
 * @param data 报告数据
 */
async function addReport(data: Omit<ReportApi.Report, 'id'>) {
  return requestClient.post('/report/add', data);
}

/**
 * 删除报告
 * @param id 报告ID
 */
async function removeReport(id: number) {
  return requestClient.delete(`/report/${id}`);
}

/**
 * 更新报告
 * @param id 报告ID
 * @param data 更新数据
 */
async function updateReport(
  id: number | string,
  data: Partial<Omit<ReportApi.Report, 'id'>>,
) {
  return requestClient.put(`/report/${id}`, data);
}

/**
 * 获取嵌套对象的值
 * @param obj 目标对象
 * @param path 路径字符串，如 'taxAnalysis.vat'
 * @returns 嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.');
  let current = obj;
  for (const key of keys) {
    if (current === null || current === undefined) return undefined;
    current = current[key];
  }
  return current;
}

/**
 * 设置嵌套对象的值
 * @param obj 目标对象
 * @param path 路径字符串，如 'taxAnalysis.vat'
 * @param value 要设置的值
 */
function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  if (keys.length === 0) return;

  const lastKey = keys[keys.length - 1];
  if (!lastKey) return;

  let target = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!key) continue;
    if (!target[key]) target[key] = {};
    target = target[key];
  }

  target[lastKey] = value;
}

/**
 * 为对象中的数组字段自动添加 index
 * @param obj 目标对象
 * @param arrayPaths 需要添加 index 的数组字段路径列表
 * @returns 处理后的对象
 */
function addIndexToArrays<T extends Record<string, any>>(
  obj: T,
  arrayPaths: string[],
): T {
  const result = structuredClone(obj);

  arrayPaths.forEach((path) => {
    const arrayValue = getNestedValue(result, path);
    if (arrayValue && Array.isArray(arrayValue)) {
      const updatedArray = arrayValue.map((item: any, index: number) => ({
        ...item,
        index,
      }));
      setNestedValue(result, path, updatedArray);
    }
  });

  return result;
}

/**
 * 获取报告概述数据
 * @param id 报告ID
 */
async function getReportResultById(id: number, level?: number) {
  const response = await requestClient.get<ReportApi.ReportResultData>(
    `/report_result/${id}`,
    {
      params: { level },
    },
  );

  let parsedResponse = response;
  if (typeof response === 'string') {
    parsedResponse = JSON.parse(response);
  }

  // 为指定的数组字段自动生成 index
  return addIndexToArrays(parsedResponse, [
    'taxAnalysis.vat',
    'taxAnalysis.stampDuty',
    'taxAnalysis.landUseTax',
    'taxAnalysis.propertyTax',
    'taxAnalysis.resourceTax',
    'taxAnalysis.consumptionTax',
    'taxAnalysis.corporateIncomeTax',
    'taxAnalysis.educationSurcharge',
    'taxAnalysis.individualIncomeTax',
    'taxAnalysis.landAppreciationTax',
    'taxAnalysis.urbanMaintenanceTax',
    'taxAnalysis.socialInsurance',
    'productAnalysis.mainSalesProducts',
    'productAnalysis.mainPurchaseProducts',
    'invoiceAnalysis.salesInvoice.riskCompanies',
    'invoiceAnalysis.salesInvoice.abnormalAccounts',
    'invoiceAnalysis.purchaseInvoice.riskCompanies',
    'invoiceAnalysis.purchaseInvoice.abnormalAccounts',
    'invoiceAnalysis.largeConsultingInvoices',
    'invoiceAnalysis.retailSupermarketInvoices',
    'basicInfo.shareholderAnalysis.crossExecutivesCompanies',
    'basicInfo.shareholderAnalysis.crossShareholdingCompanies',
    'basicInfo.shareholderAnalysis.crossLegalRepresentativeCompanies',
  ]);
}

async function updateReportResult(id: number, data: any) {
  return await requestClient.put(`/report_result/${id}`, data);
}

export {
  addReport,
  getReportList,
  getReportResultById,
  removeReport,
  updateReport,
  updateReportResult,
};
