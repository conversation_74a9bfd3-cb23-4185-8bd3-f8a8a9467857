import { useUserStore } from '@vben/stores';

import { requestClient } from '#/api/request';

export namespace memberApi {
  /** 支付订单状态枚举 */
  export type PaymentOrderStatus = 'cancelled' | 'failed' | 'paid' | 'pending';
  /**
   * 会员功能权限枚举
   */
  export const memberFeatures = {
    /** 创建报告 */
    CREATE_REPORT: 'create_report',
    /** 下载报告 */
    DOWNLOAD_REPORT: 'download_report',
    /** 数据导出 */
    DATA_EXPORT: 'data_export',
  } as const;

  /**
   * 用户会员信息接口
   */
  export interface Usermember {
    /** 用户ID */
    userId: string;
    /** 会员开始时间 */
    startDate: string;
    /** 会员结束时间 */
    endDate: string;
    /** 是否激活 */
    isActive: boolean;
    /** 可用功能列表 */
    features: string[];
    /** 剩余报告创建次数 */
    remainingReports?: number;
    /** 总报告创建次数 */
    totalReports?: number;
  }

  /**
   * 会员套餐信息接口
   */
  export interface memberPackage {
    /** 套餐ID */
    id: number;
    /** 创建时间 */
    createdAt: string;
    /** 更新时间 */
    updatedAt: string;
    /** 套餐名称 */
    name: string;
    /** 价格（元） */
    price: number;
    /** 原价（用于显示折扣） */
    originalPrice: null | number;
    /** 持续时间（月数） */
    duration: number;
    /** 报告创建次数 */
    reportCount: number;
    /** 功能列表 */
    features: string[];
    /** 套餐描述 */
    description: string;
    /** 是否为推荐套餐（0/1） */
    isPopular: number;
    /** 是否激活（0/1） */
    isActive: number;
  }

  /**
   * 支付订单信息接口
   */
  export interface PaymentOrder {
    /** 订单ID */
    orderId: string;
    /** 套餐ID */
    planId: number;
    /** 套餐名称 */
    planName?: string;
    /** 订单金额 */
    amount: number;
    /** 订单状态 */
    status: PaymentOrderStatus;
    /** 支付方式 */
    paymentMethod?: string;
    /** 支付链接 */
    paymentUrl?: string;
    /** 创建时间 */
    createdAt: string;
    /** 支付时间 */
    paidAt?: string;
    /** 退款时间 */
    refundedAt?: string;
    /** 退款原因 */
    refundReason?: string;
    /** 备注 */
    remark?: string;
  }

  /**
   * 订单列表查询参数
   */
  export interface OrderListParams {
    /** 页码 */
    page: number;
    /** 每页数量 */
    pageSize: number;
    /** 订单状态 */
    status?: PaymentOrderStatus;
    /** 支付方式 */
    paymentMethod?: string;
  }

  /**
   * 会员校验结果接口
   */
  export interface memberCheckResult {
    /** 是否为会员 */
    isMember: boolean;
    /** 是否可以创建报告 */
    canCreateReport: boolean;
    /** 剩余报告次数 */
    remainingReports: number;
    /** 会员信息 */
    memberInfo: null | Usermember;
    /** 错误信息 */
    errorMessage?: string;
  }

  /**
   * 创建套餐请求接口
   */
  export interface CreatememberPackageRequest {
    /** 套餐名称 */
    name: string;
    /** 价格（元） */
    price: number;
    /** 原价（元） */
    originalPrice?: number;
    /** 有效期（月） */
    duration: number;
    /** 报告创建次数 */
    reportCount: number;
    /** 功能列表 */
    features: string[];
    /** 套餐描述 */
    description?: string;
    /** 是否为推荐套餐 */
    isPopular: boolean;
    /** 是否启用 */
    isActive: boolean;
  }

  /**
   * 更新套餐请求接口
   */
  export interface UpdatememberPackageRequest
    extends CreatememberPackageRequest {
    /** 套餐ID */
    id: number;
  }

  /**
   * 套餐列表查询参数接口
   */
  export interface memberPackageListParams {
    /** 页码 */
    page?: number;
    /** 每页数量 */
    pageSize?: number;
    /** 套餐名称 */
    name?: string;
    /** 是否启用 */
    isActive?: boolean;
    /** 是否为推荐套餐 */
    isPopular?: boolean;
  }
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId(): string {
  const userStore = useUserStore();
  return userStore.userInfo?.userId || '';
}

/**
 * 获取用户会员信息
 */
async function getUsermember() {
  // 定义后端返回的原始数据类型
  interface UsermemberRaw extends Omit<memberApi.Usermember, 'features'> {
    features: string;
  }

  const response = await requestClient.get<UsermemberRaw>('/member/user/info');

  // 检查响应数据是否为空
  if (!response) {
    // 返回默认的非会员状态
    const defaultmember: memberApi.Usermember = {
      userId: getCurrentUserId(),
      startDate: '',
      endDate: '',
      isActive: false,
      features: [],
      remainingReports: 0,
      totalReports: 0,
    };

    return {
      success: true,
      data: defaultmember,
      message: 'ok',
    };
  }

  // 转换 features 字段从字符串到数组
  const transformedData: memberApi.Usermember = {
    ...response,
    features: response.features
      ? response.features.split(',').map((f) => f.trim())
      : [],
    userId: getCurrentUserId(),
  };

  return {
    success: true,
    data: transformedData,
    message: 'ok',
  };
}

/**
 * 获取会员套餐列表
 */
async function getmemberPackages() {
  try {
    // 定义后端返回的原始数据类型
    interface memberPackageRaw
      extends Omit<memberApi.memberPackage, 'features'> {
      features: string;
    }

    const response = await requestClient.get<memberPackageRaw[]>(
      `/member/package/plans`,
    );

    // 转换 features 字段从字符串到数组
    const transformedData: memberApi.memberPackage[] = response.map((plan) => ({
      ...plan,
      features: plan.features
        ? plan.features.split(',').map((f) => f.trim())
        : [],
    }));

    return {
      success: true,
      data: transformedData,
      message: 'ok',
    };
  } catch (error) {
    console.error('获取会员套餐列表失败:', error);
    return {
      success: false,
      data: [],
      message: '获取会员套餐列表失败',
    };
  }
}

/**
 * 创建支付订单
 */
async function createPaymentOrder(planId: number) {
  const order = await requestClient.post<memberApi.PaymentOrder>(
    `/member/order/create`,
    {
      planId,
    },
  );

  return {
    success: true,
    data: order,
    message: 'ok',
  };
}

/**
 * 查询订单状态
 */
async function checkOrderStatus(orderId: string) {
  const order = await requestClient.get<memberApi.PaymentOrder>(
    `/member/order/check/${orderId}`,
  );

  return {
    success: true,
    data: order,
    message: 'ok',
  };
}

/**
 * 获取会员套餐分页列表
 */
async function getmemberPackagesPage(
  params: memberApi.memberPackageListParams,
) {
  try {
    // 定义后端返回的原始数据类型
    interface memberPackageRaw
      extends Omit<memberApi.memberPackage, 'features'> {
      features: string;
    }

    const response = await requestClient.get<{
      records: memberPackageRaw[];
      total: number;
    }>('/member/package/page', { params });

    // 转换 features 字段从字符串到数组
    const transformedRecords: memberApi.memberPackage[] = response.records.map(
      (plan) => ({
        ...plan,
        features: plan.features
          ? plan.features.split(',').map((f) => f.trim())
          : [],
      }),
    );

    return {
      records: transformedRecords,
      total: response.total,
    };
  } catch (error) {
    console.error('获取会员套餐分页列表失败:', error);
    throw error;
  }
}

/**
 * 创建会员套餐
 */
async function creatememberPackage(data: memberApi.CreatememberPackageRequest) {
  try {
    const requestData = {
      name: data.name,
      price: data.price,
      originalPrice: data.originalPrice,
      duration: data.duration,
      reportCount: data.reportCount,
      features: Array.isArray(data.features) ? data.features.join(',') : '',
      description: data.description,
      isPopular: data.isPopular ? 1 : 0,
      isActive: data.isActive ? 1 : 0,
    };

    const response = await requestClient.post(
      '/member/package/add',
      requestData,
    );

    return {
      success: true,
      data: response,
      message: '创建套餐成功',
    };
  } catch (error) {
    console.error('创建会员套餐失败:', error);
    throw error;
  }
}

/**
 * 更新会员套餐
 */
async function updatememberPackage(data: memberApi.UpdatememberPackageRequest) {
  try {
    const requestData = {
      name: data.name,
      price: data.price,
      originalPrice: data.originalPrice,
      duration: data.duration,
      reportCount: data.reportCount,
      features: Array.isArray(data.features) ? data.features.join(',') : '',
      description: data.description,
      isPopular: data.isPopular ? 1 : 0,
      isActive: data.isActive ? 1 : 0,
    };

    const response = await requestClient.put(
      `/member/package/${data.id}`,
      requestData,
    );

    return {
      success: true,
      data: response,
      message: '更新套餐成功',
    };
  } catch (error) {
    console.error('更新会员套餐失败:', error);
    throw error;
  }
}

/**
 * 删除会员套餐
 */
async function deletememberPackage(id: number) {
  try {
    await requestClient.delete(`/member/package/${id}`);

    return {
      success: true,
      message: '删除套餐成功',
    };
  } catch (error) {
    console.error('删除会员套餐失败:', error);
    throw error;
  }
}

/**
 * 获取用户订单列表
 */
async function getUserOrdersPage(params: memberApi.OrderListParams) {
  try {
    const response = await requestClient.get<{
      records: memberApi.PaymentOrder[];
      total: number;
    }>('/member/order/page', { params });

    return {
      records: response.records,
      total: response.total,
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw error;
  }
}

export {
  checkOrderStatus,
  creatememberPackage,
  createPaymentOrder,
  deletememberPackage,
  getmemberPackages,
  getmemberPackagesPage,
  getUsermember,
  getUserOrdersPage,
  updatememberPackage,
};
