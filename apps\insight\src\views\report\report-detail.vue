<script lang="ts" setup>
import type { ReportApi } from '#/api/report/report';

import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { Card, message, Typography } from 'ant-design-vue';

import { getReportResultById } from '#/api/report/report';

import { MENU_KEYS } from './config/report-menu-config';
// 导入所有子组件
import CommodityAnalysis from './modules/commodity-analysis.vue';
import EnterpriseBasicInfoTable from './modules/enterprise-basic-info-table.vue';
import InvoiceDeclarationComparison from './modules/invoice-declaration-comparison.vue';
import PurchaseSaleAnnualAnalysis from './modules/purchase-sale-annual-analysis.vue';
import ShareholderCrossInfoTable from './modules/shareholder-cross-info-table.vue';
import TaxAnalysisDetail from './modules/tax-analysis-detail.vue';

const { Title } = Typography;

const route = useRoute();
const { setTabTitle } = useTabs();

const corpName = (route.query.corpName as string) ?? '';
const name = (route.query.name as string) ?? '';
const updatedAt = (route.query.updatedAt as string) ?? '-';
const reportId = Number(route.params.id);

// 拼接标签页标题：公司名称 - 报告名称
const reportName =
  corpName && name
    ? `${corpName} - ${name} - 详情`
    : corpName || name || '报告详情';

// 动态设置标签页标题
setTabTitle(reportName);

// 报告基本信息（保留用于头部显示）
const reportInfo = ref({
  reportName,
  reportDate: updatedAt,
});

// 报告基本信息数据
const loading = ref<boolean>(false);
const reportData = ref<null | ReportApi.ReportResultData>(null);

// 获取报告数据
const fetchReportDataData = async () => {
  if (!route.params?.id) return;

  loading.value = true;
  try {
    const response = await getReportResultById(reportId, 2);
    reportData.value = response;
  } catch (error) {
    console.error('获取报告数据失败:', error);
    message.error('获取报告数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchReportDataData();
});
</script>

<template>
  <Page auto-content-height>
    <Card class="mb-6">
      <Title :level="2" class="mb-6 text-center">税务风险分析报告详情</Title>
      <div class="mb-4 text-center">报告日期：{{ reportInfo.reportDate }}</div>
    </Card>

    <!-- 企业基本信息 -->
    <Card :id="MENU_KEYS.ENTERPRISE_BASIC_INFORMATION" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.enterprise_basic_information') }}
      </Title>
      <div :id="MENU_KEYS.BASIC_INFORMATION">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.basic_information') }}
        </Title>
        <EnterpriseBasicInfoTable :basic-info="reportData?.basicInfo || {}" />
      </div>
      <div :id="MENU_KEYS.ENTERPRISE_INFORMATION">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.enterprise_information') }}
        </Title>
        <div :id="MENU_KEYS.SHAREHOLDER_INFORMATION">
          <ShareholderCrossInfoTable
            :shareholder-analysis="reportData?.basicInfo?.shareholderAnalysis"
          />
        </div>
      </div>
    </Card>

    <!-- 发票分析 -->
    <Card :id="MENU_KEYS.COMPREHENSIVE_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.comprehensive_analysis') }}
      </Title>
      <div :id="MENU_KEYS.RELATIONSHIP_NETWORK_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.relationship_network_analysis') }}
        </Title>
        <!-- 上下游关系网络分析内容 -->
      </div>
      <div :id="MENU_KEYS.PURCHASE_INVOICE_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.purchase_invoice_analysis') }}
        </Title>
        <!-- 进项发票分析内容 -->
      </div>
      <div :id="MENU_KEYS.SALES_INVOICE_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.sales_invoice_analysis') }}
        </Title>
        <!-- 销项发票分析内容 -->
      </div>

      <!-- 进项销项金额分析 -->
      <div :id="MENU_KEYS.PURCHASE_SALE_ANNUAL_ANALYSIS">
        <PurchaseSaleAnnualAnalysis
          :invoice-analysis-data="reportData?.invoiceAnalysis || {}"
        />
      </div>

      <!-- 发票金额与申报表对比分析 -->
      <div :id="MENU_KEYS.INVOICE_AMOUNT_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.invoice_amount_analysis') }}
        </Title>
        <!-- 发票金额与申报表对比分析内容 -->
      </div>
    </Card>

    <!-- 商品分析 -->
    <Card :id="MENU_KEYS.GOODS_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.goods_analysis') }}
      </Title>
      <CommodityAnalysis :product-analysis-data="reportData?.productAnalysis" />
    </Card>

    <!-- 税务分析 -->
    <Card :id="MENU_KEYS.INVOICE_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.invoice_analysis') }}
      </Title>
      <TaxAnalysisDetail :tax-analysis-data="reportData?.taxAnalysis || {}" />
    </Card>

    <!-- 财务分析 -->
    <Card :id="MENU_KEYS.FINANCIAL_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.financial_analysis') }}
      </Title>
      <div :id="MENU_KEYS.RECEIVABLES_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.receivables_analysis') }}
        </Title>
        <!-- 应收账款分析内容 -->
      </div>
      <div :id="MENU_KEYS.INVENTORY_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.inventory_analysis') }}
        </Title>
        <!-- 存货分析内容 -->
      </div>
      <div :id="MENU_KEYS.THREE_EXPENSES_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.three_expenses_analysis') }}
        </Title>
        <!-- 三费分析内容 -->
      </div>
      <div :id="MENU_KEYS.EFFICIENCY_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.efficiency_analysis') }}
        </Title>
        <!-- 效率分析内容 -->
      </div>
      <div :id="MENU_KEYS.FINANCING_ANALYSIS">
        <Title :level="4" class="mb-4">
          {{ $t('insight.menu.financing_analysis') }}
        </Title>
        <!-- 融资分析内容 -->
      </div>
    </Card>

    <!-- 管理分析 -->
    <Card :id="MENU_KEYS.MANAGEMENT_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.management_analysis') }}
      </Title>
      <!-- 管理分析内容 -->
    </Card>

    <!-- 政府分析 -->
    <Card :id="MENU_KEYS.GOVERNMENT_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.government_analysis') }}
      </Title>
      <!-- 政府分析内容 -->
    </Card>

    <!-- 政策分析 -->
    <Card :id="MENU_KEYS.POLICY_ANALYSIS" class="mb-6">
      <Title :level="3" class="mb-4">
        {{ $t('insight.menu.policy_analysis') }}
      </Title>
      <!-- 政策分析内容 -->
    </Card>

    <!-- 审核完成按钮 -->
    <!-- <AuditCompleteButton
      :report-id="reportId"
      audit-type="detail"
      @success="handleAuditSuccess"
    /> -->
  </Page>
</template>

<style>
/* 全局样式：只有当body有report-detail-mode类时才隐藏图标 */
body.report-detail-mode .vben-menu-item .vben-menu__icon,
body.report-detail-mode .vben-sub-menu-content .vben-menu__icon {
  display: none !important;
}

/* 隐藏报告模式下的所有 iconify 图标 */
body.report-detail-mode .vben-menu__icon .iconify {
  display: none !important;
}

/* 隐藏报告模式下的默认图标 */
body.report-detail-mode .vben-menu__icon svg {
  display: none !important;
}

/* 更精确的选择器：针对报告路径的菜单项 */
body.report-detail-mode [data-path*='report-section'] .vben-menu__icon,
body.report-detail-mode
  [data-path*='report-section']
  .vben-sub-menu-content
  .vben-menu__icon {
  display: none !important;
}

/* 报告详情模式下的目录项样式 */
body.report-detail-mode .vben-menu-item__content span,
body.report-detail-mode .vben-sub-menu-content__title {
  position: relative;
  cursor: pointer;
}

/* 确保报告模式下的子菜单项也应用相同样式 */
body.report-detail-mode
  [data-path*='report-section']
  .vben-menu-item__content
  span,
body.report-detail-mode
  [data-path*='report-section']
  .vben-sub-menu-content__title {
  position: relative;
  cursor: pointer;
}

/* 为目录项添加悬停提示 */
body.report-detail-mode .vben-menu-item__content span[title]:hover::after,
body.report-detail-mode .vben-sub-menu-content__title[title]:hover::after {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  max-width: 300px;
  padding: 8px 12px;
  font-size: 12px;
  line-height: 1.4;
  color: white;
  word-wrap: break-word;
  white-space: normal;
  pointer-events: none;
  content: attr(title);
  background: rgb(0 0 0 / 85%);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  opacity: 0;
}
</style>
