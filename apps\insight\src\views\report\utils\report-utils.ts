import type { MenuItemConfig } from '../config/report-menu-config';

import { MENU_KEYS, menuConfig } from '../config/report-menu-config';

// 风险等级类型定义
export type RiskLevel = '中风险' | '低风险' | '无风险' | '高风险';

// 风险等级颜色配置接口
export interface RiskLevelConfig {
  color: string;
  textClass: string;
  bgClass: string;
}

// 风险等级颜色映射
export const riskLevelColorMap: Record<RiskLevel, RiskLevelConfig> = {
  高风险: {
    color: '#ef4444',
    textClass: 'text-red-500',
    bgClass: 'bg-red-100',
  },
  中风险: {
    color: '#f97316',
    textClass: 'text-orange-500',
    bgClass: 'bg-orange-100',
  },
  低风险: {
    color: '#eab308',
    textClass: 'text-yellow-500',
    bgClass: 'bg-yellow-100',
  },
  无风险: {
    color: '#22c55e',
    textClass: 'text-green-500',
    bgClass: 'bg-green-100',
  },
};

// 根据风险等级获取样式配置的函数
export const getRiskLevelStyle = (level: RiskLevel): RiskLevelConfig => {
  return (
    riskLevelColorMap[level] || {
      color: '#6b7280',
      textClass: 'text-gray-500',
      bgClass: 'bg-gray-100',
    }
  );
};

// 风险等级工具函数
export const riskLevelUtils = {
  // 获取所有风险等级
  getAllRiskLevels: (): RiskLevel[] => {
    return Object.keys(riskLevelColorMap) as RiskLevel[];
  },

  // 验证风险等级是否有效
  isValidRiskLevel: (level: string): level is RiskLevel => {
    return Object.keys(riskLevelColorMap).includes(level);
  },

  // 获取风险等级的颜色值
  getRiskLevelColor: (level: RiskLevel): string => {
    return getRiskLevelStyle(level).color;
  },

  // 获取风险等级的文本样式类
  getRiskLevelTextClass: (level: RiskLevel): string => {
    return getRiskLevelStyle(level).textClass;
  },

  // 获取风险等级的背景样式类
  getRiskLevelBgClass: (level: RiskLevel): string => {
    return getRiskLevelStyle(level).bgClass;
  },
};

// 风险等级图标映射
export const riskLevelIconMap: Record<RiskLevel, string> = {
  高风险: 'i-ant-design:exclamation-circle-filled',
  中风险: 'i-ant-design:warning-filled',
  低风险: 'i-ant-design:info-circle-filled',
  无风险: 'i-ant-design:check-circle-filled',
};

// 获取风险等级颜色
export const getRiskLevelColor = (level: RiskLevel): string => {
  return riskLevelUtils.getRiskLevelColor(level);
};

// 获取风险等级图标
export const getRiskLevelIcon = (level: RiskLevel): string => {
  return riskLevelIconMap[level] || 'i-ant-design:question-circle-filled';
};

// 目录工具函数
export const menuUtils = {
  // 根据key查找菜单项
  findMenuItemByKey: (key: string): MenuItemConfig | null => {
    const findInConfig = (items: MenuItemConfig[]): MenuItemConfig | null => {
      for (const item of items) {
        if (item.key === key) return item;
        if (item.children) {
          const found = findInConfig(item.children);
          if (found) return found;
        }
      }
      return null;
    };
    return findInConfig(menuConfig);
  },

  // 获取所有菜单项的key列表
  getAllMenuKeys: (): string[] => {
    const keys: string[] = [];
    const collectKeys = (items: MenuItemConfig[]) => {
      items.forEach((item) => {
        keys.push(item.key);
        if (item.children) {
          collectKeys(item.children);
        }
      });
    };
    collectKeys(menuConfig);
    return keys;
  },

  // 获取父级菜单项
  getParentMenuKey: (childKey: string): null | string => {
    const findParent = (
      items: MenuItemConfig[],
      targetKey: string,
    ): null | string => {
      for (const item of items) {
        if (item.children?.some((child) => child.key === targetKey)) {
          return item.key;
        }
        if (item.children) {
          const found = findParent(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };
    return findParent(menuConfig, childKey);
  },

  // 验证菜单项是否存在
  isValidMenuKey: (key: string): boolean => {
    return menuUtils.getAllMenuKeys().includes(key);
  },

  // 获取菜单项的层级深度
  getMenuLevel: (key: string): number => {
    const item = menuUtils.findMenuItemByKey(key);
    return item?.level || 1;
  },

  // 根据常量获取key
  getKeyByConstant: (constant: keyof typeof MENU_KEYS): string => {
    return MENU_KEYS[constant];
  },
};

// 企业状态映射
export const companyStateMap: Record<number, string> = {
  [-1]: '未知',
  0: '在营',
  1: '开业',
  2: '在册',
  3: '注销',
};

// 是否类型映射 (0:否、1:是)
export const yesNoMap: Record<number, string> = {
  0: '否',
  1: '是',
};

// 企业信息字段转义工具函数
export const corpInfoUtils = {
  // 转换企业状态
  getCompanyStateText: (state: number): string => {
    return companyStateMap[state] || '未知';
  },

  // 转换是否出口企业
  getExportCompanyText: (isExport: number): string => {
    return yesNoMap[isExport] || '否';
  },

  // 转换是否高新技术企业
  getHighTechText: (isHighTech: number): string => {
    return yesNoMap[isHighTech] || '否';
  },

  // 转换是否小微企业
  getSmallMicroText: (isSmallMicro: number): string => {
    return yesNoMap[isSmallMicro] || '否';
  },

  // 获取企业状态选项列表 (用于编辑时的选择框)
  getCompanyStateOptions: () => {
    return Object.entries(companyStateMap).map(([value, label]) => ({
      label,
      value: Number(value),
    }));
  },

  // 获取是否选项列表 (用于编辑时的选择框)
  getYesNoOptions: () => {
    return Object.entries(yesNoMap).map(([value, label]) => ({
      label,
      value: Number(value),
    }));
  },
};

// 数值格式化工具函数
export const formatUtils = {
  // 格式化数值为中文格式，保留2位小数
  formatNumber: (value: null | number | undefined): string => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value || 0);
  },

  // 格式化差额数值，负数显示负号
  formatDifference: (value: null | number | undefined): string => {
    const formatted = new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Math.abs(value || 0));
    return value && value >= 0 ? formatted : `-${formatted}`;
  },

  // 格式化万元单位的数值
  formatWanYuan: (value: null | number | undefined): number => {
    return Math.round(Number(value || 0) / 10_000);
  },

  // 获取差额的颜色样式
  getDifferenceColor: (value: null | number | undefined): string => {
    return value && value >= 0 ? '#52c41a' : '#ff4d4f';
  },

  // 表格数值列的通用渲染函数
  createNumberRenderer: () => {
    return ({ text }: { text: number }) => {
      return formatUtils.formatNumber(text);
    };
  },

  // 表格差额列的通用渲染函数
  createDifferenceRenderer: () => {
    return ({ text }: { text: number }) => {
      return formatUtils.formatDifference(text);
    };
  },

  // 表格差额列的通用样式函数
  createDifferenceCell: () => {
    return (record: any) => {
      return {
        style: {
          color: formatUtils.getDifferenceColor(record.diff),
        },
      };
    };
  },
};

// 滚动到指定章节的通用函数
export const scrollToSection = (sectionKey: string) => {
  try {
    const targetElement: HTMLElement | null = document.querySelector(
      `#${CSS.escape(sectionKey)}`,
    );
    const contentContainer: Element | null =
      document.querySelector('.content-container');

    if (!targetElement) {
      console.warn('目标元素未找到');
      return;
    }

    if (!contentContainer) {
      console.warn('内容容器未找到');
      return;
    }

    if (
      !('scrollTo' in contentContainer) ||
      !('scrollTop' in contentContainer)
    ) {
      console.warn('内容容器不支持滚动');
      return;
    }

    // 计算元素在内容容器中的相对位置
    const containerRect: DOMRect = contentContainer.getBoundingClientRect();
    const elementRect: DOMRect = targetElement.getBoundingClientRect();
    const scrollOffset = 20;
    const scrollTop: number =
      (contentContainer as HTMLElement).scrollTop +
      elementRect.top -
      containerRect.top -
      scrollOffset;

    // 平滑滚动到目标位置
    (contentContainer as HTMLElement).scrollTo({
      top: Math.max(0, scrollTop),
      behavior: 'auto',
    });
  } catch (error) {
    console.error('目录滚动出错：', error);
  }
};
