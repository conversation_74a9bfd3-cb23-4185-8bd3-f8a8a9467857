<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { RiskLevel } from '../utils/report-utils';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Table, Tag, Typography } from 'ant-design-vue';

import { formatUtils, getRiskLevelStyle } from '../utils/report-utils';

// 分析类型枚举
type AnalysisType = 'purchase-sale' | 'invoice-declaration';

interface Props {
  // 分析类型
  analysisType: AnalysisType;
  // 发票分析数据
  invoiceAnalysisData?: {
    // 进项销项分析相关
    isOnlySales?: boolean;
    isPurchaseGTSalesTotal?: boolean;
    isSalesMLTPurchaseTax?: boolean;
    taxExemptComparisons?: Array<{
      diff: number;
      purchase: number;
      sale: number;
      year: number;
    }>;
    // 发票申报对比分析相关
    salesVsDeclares?: Array<{
      decl: number;
      diff: number;
      sale: number;
      year: number;
    }>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  invoiceAnalysisData: () => ({}),
});

const { Title } = Typography;

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 获取布尔值风险等级
const getBooleanRiskLevel = (value?: boolean): RiskLevel => {
  if (value === undefined || value === null) {
    return '无风险';
  }
  return value ? '高风险' : '无风险';
};

// 配置映射
const analysisConfig = computed(() => {
  const configs = {
    'purchase-sale': {
      title: '4.进项销项金额分析',
      chartTitle: '进项销项金额（年度）分析',
      dataSource: 'taxExemptComparisons',
      columns: [
        {
          title: '年度',
          dataIndex: 'year',
          key: 'year',
          align: 'center' as const,
          width: 100,
        },
        {
          title: '进项不含税金额(元)',
          dataIndex: 'purchase',
          key: 'purchase',
          align: 'right' as const,
          width: 180,
          customRender: formatUtils.createNumberRenderer(),
        },
        {
          title: '销项不含税金额(元)',
          dataIndex: 'sale',
          key: 'sale',
          align: 'right' as const,
          width: 180,
          customRender: formatUtils.createNumberRenderer(),
        },
        {
          title: '销项减进项(元)',
          dataIndex: 'diff',
          key: 'diff',
          align: 'right' as const,
          width: 150,
          customRender: formatUtils.createDifferenceRenderer(),
          customCell: formatUtils.createDifferenceCell(),
        },
      ],
      legendData: ['进项不含税金额(万)', '销项不含税金额(万)'],
      seriesConfig: [
        {
          name: '进项不含税金额(万)',
          dataKey: 'purchase',
          color: '#5470c6',
        },
        {
          name: '销项不含税金额(万)',
          dataKey: 'sale',
          color: '#91cc75',
        },
      ],
      showDetectionItems: true,
    },
    'invoice-declaration': {
      title: '5.发票金额与申报表对比分析',
      chartTitle: '发票金额与申报表对比分析',
      dataSource: 'salesVsDeclares',
      columns: [
        {
          title: '年度',
          dataIndex: 'year',
          key: 'year',
          align: 'center' as const,
          width: 100,
        },
        {
          title: '申报营业收入(元)',
          dataIndex: 'decl',
          key: 'decl',
          align: 'right' as const,
          width: 180,
          customRender: formatUtils.createNumberRenderer(),
        },
        {
          title: '销项不含税金额(元)',
          dataIndex: 'sale',
          key: 'sale',
          align: 'right' as const,
          width: 180,
          customRender: formatUtils.createNumberRenderer(),
        },
        {
          title: '申报减开票差额(元)',
          dataIndex: 'diff',
          key: 'diff',
          align: 'right' as const,
          width: 180,
          customRender: formatUtils.createDifferenceRenderer(),
          customCell: formatUtils.createDifferenceCell(),
        },
      ],
      legendData: ['申报营业收入(万)', '销项不含税金额(万)'],
      seriesConfig: [
        {
          name: '申报营业收入(万)',
          dataKey: 'decl',
          color: '#5470c6',
        },
        {
          name: '销项不含税金额(万)',
          dataKey: 'sale',
          color: '#91cc75',
        },
      ],
      showDetectionItems: false,
    },
  };
  
  return configs[props.analysisType];
});

// 检测项目（仅进项销项分析显示）
const detectionItems = computed(() => {
  if (props.analysisType !== 'purchase-sale') return [];
  
  return [
    {
      item: '进项是否大于销项',
      result: getBooleanRiskLevel(
        props.invoiceAnalysisData?.isPurchaseGTSalesTotal,
      ),
    },
    {
      item: '销项税额是否远小于进项税额',
      result: getBooleanRiskLevel(
        props.invoiceAnalysisData?.isSalesMLTPurchaseTax,
      ),
    },
    {
      item: '是否有销无进(少进)',
      result: getBooleanRiskLevel(props.invoiceAnalysisData?.isOnlySales),
    },
  ];
});

// 表格数据
const tableData = computed(() => {
  const config = analysisConfig.value;
  const data = props.invoiceAnalysisData?.[config.dataSource as keyof typeof props.invoiceAnalysisData] || [];
  
  return (data as any[]).map((item, index) => ({
    key: index,
    year: item.year,
    purchase: item.purchase,
    sale: item.sale,
    decl: item.decl,
    diff: item.diff,
  }));
});

// 图表配置
const getChartOption = () => {
  const config = analysisConfig.value;
  const data = props.invoiceAnalysisData?.[config.dataSource as keyof typeof props.invoiceAnalysisData] || [];
  
  const years = (data as any[]).map((item) => item.year);
  
  // 根据配置生成系列数据
  const seriesData = config.seriesConfig.map((seriesConfig) => ({
    name: seriesConfig.name,
    type: 'bar' as const,
    data: (data as any[]).map((item) => formatUtils.formatWanYuan(item[seriesConfig.dataKey])),
    itemStyle: {
      color: seriesConfig.color,
    },
    label: {
      show: true,
      position: 'top' as const,
      formatter: '{c}',
    },
  }));

  return {
    title: {
      text: config.chartTitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold' as const,
      },
    },
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'cross' as const,
        crossStyle: {
          color: '#999',
        },
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}年<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}万元<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: config.legendData,
      top: 30,
    },
    xAxis: [
      {
        type: 'category' as const,
        data: years,
        axisPointer: {
          type: 'shadow' as const,
        },
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    yAxis: [
      {
        type: 'value' as const,
        name: '金额(万元)',
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: seriesData,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
  };
};

// 渲染图表
const renderChart = () => {
  nextTick(() => {
    if (!chartRef.value) {
      console.warn('Chart ref not available');
      return;
    }

    const option = getChartOption();
    renderEcharts(option).catch((error) => {
      console.error('Chart render error:', error);
    });
  });
};

watch(() => props.invoiceAnalysisData, renderChart);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <Title :level="4" class="mb-4">{{ analysisConfig.title }}</Title>
  
  <!-- 检测项目（仅进项销项分析显示） -->
  <div v-if="analysisConfig.showDetectionItems" class="mb-6">
    <h5 class="mb-3 text-base font-medium">检测项目：</h5>
    <div class="space-y-2">
      <div
        v-for="(item, index) in detectionItems"
        :key="index"
        class="flex items-center justify-between rounded-lg bg-gray-50 p-3"
      >
        <span class="text-gray-700">{{ item.item }}</span>
        <Tag :color="getRiskLevelStyle(item.result).color" class="font-medium">
          {{ item.result }}
        </Tag>
      </div>
    </div>
  </div>
  
  <div class="mb-6 flex justify-center">
    <Table
      :columns="analysisConfig.columns"
      :data-source="tableData"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ x: 'max-content' }"
    />
  </div>
  
  <div style="margin-top: 24px; width: 100%">
    <EchartsUI ref="chartRef" height="400px" />
  </div>
</template>
