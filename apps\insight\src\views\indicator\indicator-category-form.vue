<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';

import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import {
  createIndicatorCategory,
  IndicatorApi,
  updateIndicatorCategory,
} from '#/api/indicator';

const emit = defineEmits<{
  success: [];
}>();

const formData = ref<IndicatorApi.IndicatorCategory | null>(null);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  closeOnPressEscape: false,
  draggable: true,
  onOpened: () => {
    formData.value = modalApi.getData<IndicatorApi.IndicatorCategory>();

    if (formData.value && formData.value.id) {
      // 编辑模式，设置表单值
      form.setValues({
        name: formData.value.name,
        level: formData.value.level,
        description: formData.value.description,
        status: formData.value.status === 1,
      });
    } else {
      // 新增模式，重置表单并设置默认值
      form.resetForm();
      form.setValues({
        status: true,
        level: 1,
      });
    }
  },
});

/**
 * 表单配置
 */
const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '分类名称',
      },
      fieldName: 'name',
      label: '分类名称',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '级别',
        min: 1,
        max: 10,
        style: { width: '100%' },
      },
      fieldName: 'level',
      label: '级别',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '禁用',
        style: { width: '80px' },
      },
      fieldName: 'status',
      label: '状态',
      defaultValue: true,
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '分类描述',
        rows: 3,
      },
      fieldName: 'description',
      label: '分类描述',
      formItemClass: 'sm:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
};

const [Form, form] = useVbenForm(formOptions);

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    const validateResult = await form.validate();
    if (
      !validateResult ||
      (validateResult && !validateResult.valid) ||
      (validateResult &&
        validateResult.errors &&
        Object.keys(validateResult.errors).length > 0)
    ) {
      message.error('请填写所有必填项');
      return;
    }

    const values = await form.getValues();
    const requestData: IndicatorApi.CreateIndicatorCategoryRequest = {
      name: values.name,
      level: values.level,
      description: values.description,
      status: values.status ? 1 : 0,
    };

    if (formData.value && formData.value.id) {
      // 编辑模式
      await updateIndicatorCategory({
        ...requestData,
        id: formData.value.id,
      });
      message.success('更新成功');
    } else {
      // 新增模式
      await createIndicatorCategory(requestData);
      message.success('创建成功');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('保存指标分类失败:', error);
    message.error('保存指标分类失败');
  }
}

function handleCancel() {
  modalApi.close();
}
</script>

<template>
  <Modal
    :title="formData && formData.id ? '编辑指标分类' : '新增指标分类'"
    :width="700"
  >
    <Form />
    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="handleCancel"> 取消 </Button>
        <Button type="primary" @click="handleSubmit"> 保存 </Button>
      </div>
    </template>
  </Modal>
</template>
