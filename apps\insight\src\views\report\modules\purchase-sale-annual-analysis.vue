<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { RiskLevel } from '../utils/report-utils';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Table, Tag, Typography } from 'ant-design-vue';

import { getRiskLevelStyle } from '../utils/report-utils';

interface Props {
  invoiceAnalysisData?: {
    isOnlySales?: boolean;
    isPurchaseGTSalesTotal?: boolean;
    isSalesMLTPurchaseTax?: boolean;
    taxExemptComparisons?: Array<{
      diff: number;
      purchase: number;
      sale: number;
      year: number;
    }>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  invoiceAnalysisData: () => ({}),
});

const { Title } = Typography;

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const getBooleanRiskLevel = (value?: boolean): RiskLevel => {
  if (value === undefined || value === null) {
    return '无风险';
  }
  return value ? '高风险' : '无风险';
};

const detectionItems = computed(() => [
  {
    item: '进项是否大于销项',
    result: getBooleanRiskLevel(
      props.invoiceAnalysisData?.isPurchaseGTSalesTotal,
    ),
  },
  {
    item: '销项税额是否远小于进项税额',
    result: getBooleanRiskLevel(
      props.invoiceAnalysisData?.isSalesMLTPurchaseTax,
    ),
  },
  {
    item: '是否有销无进(少进)',
    result: getBooleanRiskLevel(props.invoiceAnalysisData?.isOnlySales),
  },
]);

const tableColumns = [
  {
    title: '年度',
    dataIndex: 'year',
    key: 'year',
    align: 'center' as const,
    width: 100,
  },
  {
    title: '进项不含税金额',
    dataIndex: 'purchase',
    key: 'purchase',
    align: 'right' as const,
    width: 180,
    customRender: ({ text }: { text: number }) => {
      return text || '0.00';
    },
  },
  {
    title: '销项不含税金额',
    dataIndex: 'sale',
    key: 'sale',
    align: 'right' as const,
    width: 180,
    customRender: ({ text }: { text: number }) => {
      return text || '0.00';
    },
  },
  {
    title: '销项减进项',
    dataIndex: 'diff',
    key: 'diff',
    align: 'right' as const,
    width: 150,
    customRender: ({ text }: { text: number }) => {
      return text || '0.00';
    },
  },
];

const tableData = computed(() => {
  const data = props.invoiceAnalysisData?.taxExemptComparisons || [];

  return data.map((item, index) => ({
    key: index,
    year: item.year,
    purchase: item.purchase,
    sale: item.sale,
    diff: item.diff,
  }));
});

const getChartOption = () => {
  const data = props.invoiceAnalysisData?.taxExemptComparisons || [];

  const years = data.map((item) => item.year);
  const purchaseData = data.map((item) => {
    const value = Number(item.purchase) / 10_000;
    return Math.round(value);
  });
  const saleData = data.map((item) => {
    const value = Number(item.sale) / 10_000;
    return Math.round(value);
  });

  return {
    title: {
      text: '进项销项金额（年度）分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold' as const,
      },
    },
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'cross' as const,
        crossStyle: {
          color: '#999',
        },
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}年<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}万元<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ['进项不含税金额(万)', '销项不含税金额(万)'],
      top: 30,
    },
    xAxis: [
      {
        type: 'category' as const,
        data: years,
        axisPointer: {
          type: 'shadow' as const,
        },
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    yAxis: [
      {
        type: 'value' as const,
        name: '金额(万元)',
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: [
      {
        name: '进项不含税金额(万)',
        type: 'bar' as const,
        data: purchaseData,
        itemStyle: {
          color: '#5470c6',
        },
        label: {
          show: true,
          position: 'top' as const,
          formatter: '{c}',
        },
      },
      {
        name: '销项不含税金额(万)',
        type: 'bar' as const,
        data: saleData,
        itemStyle: {
          color: '#91cc75',
        },
        label: {
          show: true,
          position: 'top' as const,
          formatter: '{c}',
        },
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
  };
};

const renderChart = () => {
  nextTick(() => {
    if (!chartRef.value) {
      console.warn('Chart ref not available');
      return;
    }

    const option = getChartOption();
    renderEcharts(option).catch((error) => {
      console.error('Chart render error:', error);
    });
  });
};

watch(() => props.invoiceAnalysisData, renderChart);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <Title :level="4" class="mb-4"> 4.进项销项金额分析 </Title>
  <div class="mb-6">
    <h5 class="mb-3 text-base font-medium">检测项目：</h5>
    <div class="space-y-2">
      <div
        v-for="(item, index) in detectionItems"
        :key="index"
        class="flex items-center justify-between rounded-lg bg-gray-50 p-3"
      >
        <span class="text-gray-700">{{ item.item }}</span>
        <Tag :color="getRiskLevelStyle(item.result).color" class="font-medium">
          {{ item.result }}
        </Tag>
      </div>
    </div>
  </div>
  <div class="mb-6 flex justify-center">
    <Table
      :columns="tableColumns"
      :data-source="tableData"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ x: 'max-content' }"
    />
  </div>
  <div style="margin-top: 24px; width: 100%">
    <EchartsUI ref="chartRef" height="400px" />
  </div>
</template>
