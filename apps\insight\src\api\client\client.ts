import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace ClientApi {
  export interface Client {
    [key: string]: any;

    // 记录ID
    id: number;

    // 报告名称
    name: string;

    // 更新时间
    updatedAt: string;

    // 公司ID
    corpId: number;

    // 登录模式
    loginMode: number;

    // 登录手机号
    loginPhone: string;

    // 登录密码
    loginPassword: string;

    // 标签
    tags: string;

    // 公司名称
    corpName: string;

    // 税号
    taxId: string;
  }
}

/**
 * 添加客户
 * @param data 客户数据
 */
async function addClient(data: Omit<ClientApi.Client, 'id'>) {
  return requestClient.post('/client/add', data);
}

/**
 * 删除客户
 * @param id 客户ID
 */
async function removeClient(id: number) {
  return requestClient.delete(`/client/${id}`);
}

/**
 * 更新客户
 *
 * @param id 客户ID
 * @param data 角色数据
 */
async function updateClient(id: number, data: Omit<ClientApi.Client, 'id'>) {
  return requestClient.put(`/client/${id}`, data);
}

/**
 * 获取客户列表
 */
async function getClientsPage(params: Recordable<any>) {
  return requestClient.get<{ records: any; total: number }>('/client/page', {
    params,
  });
}

export { addClient, getClientsPage, removeClient, updateClient };
