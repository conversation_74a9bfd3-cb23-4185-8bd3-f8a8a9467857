<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { RiskLevel } from '../utils/report-utils';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Table, Tag, Typography } from 'ant-design-vue';

import { getRiskLevelStyle } from '../utils/report-utils';

interface Props {
  invoiceAnalysisData?: {
    salesVsDeclares?: Array<{
      decl: number;
      diff: number;
      sale: number;
      year: number;
    }>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  invoiceAnalysisData: () => ({}),
});

const { Title } = Typography;

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 表格列定义
const tableColumns = [
  {
    title: '年度',
    dataIndex: 'year',
    key: 'year',
    align: 'center' as const,
    width: 100,
  },
  {
    title: '①申报营业收入(元)',
    dataIndex: 'decl',
    key: 'decl',
    align: 'right' as const,
    width: 180,
    customRender: ({ text }: { text: number }) => {
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(text || 0);
    },
  },
  {
    title: '②销项不含税金额(元)',
    dataIndex: 'sale',
    key: 'sale',
    align: 'right' as const,
    width: 180,
    customRender: ({ text }: { text: number }) => {
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(text || 0);
    },
  },
  {
    title: '申报减开票差额 ③=①-②',
    dataIndex: 'diff',
    key: 'diff',
    align: 'right' as const,
    width: 180,
    customRender: ({ text }: { text: number }) => {
      const formatted = new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(Math.abs(text || 0));
      return text >= 0 ? formatted : `-${formatted}`;
    },
    customCell: (record: any) => {
      return {
        style: {
          color: record.diff >= 0 ? '#52c41a' : '#ff4d4f',
        },
      };
    },
  },
];

// 表格数据
const tableData = computed(() => {
  const data = props.invoiceAnalysisData?.salesVsDeclares || [];

  return data.map((item, index) => ({
    key: index,
    year: item.year,
    decl: item.decl,
    sale: item.sale,
    diff: item.diff,
  }));
});

// 图表配置
const getChartOption = () => {
  const data = props.invoiceAnalysisData?.salesVsDeclares || [];

  const years = data.map((item) => item.year);
  const declData = data.map((item) => {
    const value = Number(item.decl) / 10_000;
    return Math.round(value);
  });
  const saleData = data.map((item) => {
    const value = Number(item.sale) / 10_000;
    return Math.round(value);
  });

  return {
    title: {
      text: '发票金额与申报表对比分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold' as const,
      },
    },
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'cross' as const,
        crossStyle: {
          color: '#999',
        },
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}年<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}万元<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ['申报营业收入(万)', '销项不含税金额(万)'],
      top: 30,
    },
    xAxis: [
      {
        type: 'category' as const,
        data: years,
        axisPointer: {
          type: 'shadow' as const,
        },
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    yAxis: [
      {
        type: 'value' as const,
        name: '金额(万元)',
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: [
      {
        name: '申报营业收入(万)',
        type: 'bar' as const,
        data: declData,
        itemStyle: {
          color: '#5470c6',
        },
        label: {
          show: true,
          position: 'top' as const,
          formatter: '{c}',
        },
      },
      {
        name: '销项不含税金额(万)',
        type: 'bar' as const,
        data: saleData,
        itemStyle: {
          color: '#91cc75',
        },
        label: {
          show: true,
          position: 'top' as const,
          formatter: '{c}',
        },
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
  };
};

// 渲染图表
const renderChart = () => {
  nextTick(() => {
    if (!chartRef.value) {
      console.warn('Chart ref not available');
      return;
    }

    const option = getChartOption();
    renderEcharts(option).catch((error) => {
      console.error('Chart render error:', error);
    });
  });
};

watch(() => props.invoiceAnalysisData, renderChart);

onMounted(() => {
  renderChart();
});
</script>

<template>
  <Title :level="4" class="mb-4"> 5.发票金额与申报表对比分析 </Title>
  
  <div class="mb-6 flex justify-center">
    <Table
      :columns="tableColumns"
      :data-source="tableData"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ x: 'max-content' }"
    />
  </div>
  
  <div style="margin-top: 24px; width: 100%">
    <EchartsUI ref="chartRef" height="400px" />
  </div>
</template>
