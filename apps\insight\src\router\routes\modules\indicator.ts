import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:list',
      keepAlive: true,
      order: 1003,
      title: $t('insight.indicator.config-management'),
    },
    name: 'IndicatorConfigManagement',
    path: '/indicator/config',
    component: () => import('#/views/indicator/indicator-management.vue'),
  },
];

export default routes;
