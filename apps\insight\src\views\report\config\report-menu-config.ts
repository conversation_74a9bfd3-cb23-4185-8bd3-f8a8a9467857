import type { MenuProps } from 'ant-design-vue';

import { $t } from '#/locales';

// 多级目录配置数据结构
export interface MenuItemConfig {
  key: string;
  label: string;
  level?: number;
  icon?: string;
  disabled?: boolean;
  children?: MenuItemConfig[];
}

// 目录配置常量
export const MENU_KEYS = {
  ENTERPRISE_BASIC_INFORMATION: 'enterprise_basic_information',
  BASIC_INFORMATION: 'basic_information',
  ENTERPRISE_INFORMATION: 'enterprise_information',
  SHAREHOLDER_INFORMATION: 'shareholder_information',
  EXECUTIVE_CROSS_INFO: 'executive_cross_info',
  LEGAL_PERSON_CROSS_INFO: 'legal_person_cross_info',
  COMPREHENSIVE_ANALYSIS: 'comprehensive_analysis',
  RELATIONSHIP_NETWORK_ANALYSIS: 'relationship_network_analysis',
  PURCHASE_INVOICE_ANALYSIS: 'purchase_invoice_analysis',
  SALES_INVOICE_ANALYSIS: 'sales_invoice_analysis',
  PURCHASE_SALE_ANNUAL_ANALYSIS: 'purchase_sale_annual_analysis',
  INVOICE_AMOUNT_ANALYSIS: 'invoice_amount_analysis',
  GOODS_ANALYSIS: 'goods_analysis',
  MAIN_INPUT_GOODS: 'main_input_goods',
  MAIN_OUTPUT_GOODS: 'main_output_goods',
  ANALYSIS_CONCLUSION: 'analysis_conclusion',
  INVOICE_ANALYSIS: 'invoice_analysis',
  ENTERPRISE_INCOME_TAX: 'enterprise_income_tax',
  VALUE_ADDED_TAX: 'value_added_tax',
  PROPERTY_TAX: 'property_tax',
  URBAN_LAND_TAX: 'urban_land_tax',
  CONSUMPTION_TAX: 'consumption_tax',
  STAMP_TAX: 'stamp_tax',
  URBAN_CONSTRUCTION_TAX: 'urban_construction_tax',
  EDUCATION_EXPENSE: 'education_expense',
  LAND_APPRECIATION_TAX: 'land_appreciation_tax',
  RESOURCE_TAX: 'resource_tax',
  PERSONAL_INCOME_TAX: 'personal_income_tax',
  SOCIAL_INSURANCE: 'social_insurance',
  FINANCIAL_ANALYSIS: 'financial_analysis',
  RECEIVABLES_ANALYSIS: 'receivables_analysis',
  INVENTORY_ANALYSIS: 'inventory_analysis',
  THREE_EXPENSES_ANALYSIS: 'three_expenses_analysis',
  EFFICIENCY_ANALYSIS: 'efficiency_analysis',
  FINANCING_ANALYSIS: 'financing_analysis',
  MANAGEMENT_ANALYSIS: 'management_analysis',
  GOVERNMENT_ANALYSIS: 'government_analysis',
  POLICY_ANALYSIS: 'policy_analysis',
};

// 目录配置数据
export const menuConfig: MenuItemConfig[] = [
  {
    key: MENU_KEYS.ENTERPRISE_BASIC_INFORMATION,
    label: $t('insight.menu.enterprise_basic_information'),
    level: 3,
    children: [
      {
        key: MENU_KEYS.BASIC_INFORMATION,
        label: $t('insight.menu.basic_information'),
        level: 4,
      },
      {
        key: MENU_KEYS.ENTERPRISE_INFORMATION,
        label: $t('insight.menu.enterprise_information'),
        level: 4,
        children: [
          {
            key: MENU_KEYS.SHAREHOLDER_INFORMATION,
            label: $t('insight.menu.shareholder_information'),
            level: 5,
          },
          {
            key: MENU_KEYS.EXECUTIVE_CROSS_INFO,
            label: $t('insight.menu.executive_cross_info'),
            level: 5,
          },
          {
            key: MENU_KEYS.LEGAL_PERSON_CROSS_INFO,
            label: $t('insight.menu.legal_person_cross_info'),
            level: 5,
          },
        ],
      },
    ],
  },
  {
    key: MENU_KEYS.COMPREHENSIVE_ANALYSIS,
    label: $t('insight.menu.comprehensive_analysis'),
    level: 3,
    children: [
      {
        key: MENU_KEYS.RELATIONSHIP_NETWORK_ANALYSIS,
        label: $t('insight.menu.relationship_network_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.PURCHASE_INVOICE_ANALYSIS,
        label: $t('insight.menu.purchase_invoice_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.SALES_INVOICE_ANALYSIS,
        label: $t('insight.menu.sales_invoice_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.PURCHASE_SALE_ANNUAL_ANALYSIS,
        label: $t('insight.menu.purchase_sale_annual_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.INVOICE_AMOUNT_ANALYSIS,
        label: $t('insight.menu.invoice_amount_analysis'),
        level: 4,
      },
    ],
  },
  {
    key: MENU_KEYS.GOODS_ANALYSIS,
    label: $t('insight.menu.goods_analysis'),
    level: 3,
    children: [
      {
        key: MENU_KEYS.MAIN_INPUT_GOODS,
        label: $t('insight.menu.main_input_goods'),
        level: 4,
      },
      {
        key: MENU_KEYS.MAIN_OUTPUT_GOODS,
        label: $t('insight.menu.main_output_goods'),
        level: 4,
      },
      {
        key: MENU_KEYS.ANALYSIS_CONCLUSION,
        label: $t('insight.menu.analysis_conclusion'),
        level: 4,
      },
    ],
  },
  {
    key: MENU_KEYS.INVOICE_ANALYSIS,
    label: $t('insight.menu.invoice_analysis'),
    level: 3,
    children: [
      {
        key: MENU_KEYS.ENTERPRISE_INCOME_TAX,
        label: $t('insight.menu.enterprise_income_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.VALUE_ADDED_TAX,
        label: $t('insight.menu.value_added_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.PROPERTY_TAX,
        label: $t('insight.menu.property_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.URBAN_LAND_TAX,
        label: $t('insight.menu.urban_land_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.CONSUMPTION_TAX,
        label: $t('insight.menu.consumption_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.STAMP_TAX,
        label: $t('insight.menu.stamp_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.URBAN_CONSTRUCTION_TAX,
        label: $t('insight.menu.urban_construction_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.EDUCATION_EXPENSE,
        label: $t('insight.menu.education_expense'),
        level: 4,
      },
      {
        key: MENU_KEYS.LAND_APPRECIATION_TAX,
        label: $t('insight.menu.land_appreciation_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.RESOURCE_TAX,
        label: $t('insight.menu.resource_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.PERSONAL_INCOME_TAX,
        label: $t('insight.menu.personal_income_tax'),
        level: 4,
      },
      {
        key: MENU_KEYS.SOCIAL_INSURANCE,
        label: $t('insight.menu.social_insurance'),
        level: 4,
      },
    ],
  },
  {
    key: MENU_KEYS.FINANCIAL_ANALYSIS,
    label: $t('insight.menu.financial_analysis'),
    level: 3,
    children: [
      {
        key: MENU_KEYS.RECEIVABLES_ANALYSIS,
        label: $t('insight.menu.receivables_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.INVENTORY_ANALYSIS,
        label: $t('insight.menu.inventory_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.THREE_EXPENSES_ANALYSIS,
        label: $t('insight.menu.three_expenses_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.EFFICIENCY_ANALYSIS,
        label: $t('insight.menu.efficiency_analysis'),
        level: 4,
      },
      {
        key: MENU_KEYS.FINANCING_ANALYSIS,
        label: $t('insight.menu.financing_analysis'),
        level: 4,
      },
    ],
  },
  {
    key: MENU_KEYS.MANAGEMENT_ANALYSIS,
    label: $t('insight.menu.management_analysis'),
    level: 3,
  },
  {
    key: MENU_KEYS.GOVERNMENT_ANALYSIS,
    label: $t('insight.menu.government_analysis'),
    level: 3,
  },
  {
    key: MENU_KEYS.POLICY_ANALYSIS,
    label: $t('insight.menu.policy_analysis'),
    level: 3,
  },
];

export const transformMenuConfig = (
  config: MenuItemConfig[],
): MenuProps['items'] => {
  return config.map((item) => ({
    key: item.key,
    title: item.label,
    label: item.label,
    disabled: item.disabled,
    children: item.children ? transformMenuConfig(item.children) : undefined,
  }));
};
