import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  interface UserInfoRaw extends Omit<UserInfo, 'roles'> {
    roles: string;
  }

  const response = await requestClient.get<UserInfoRaw>('/user/info');

  const transformedData: UserInfo = {
    ...response,
    roles: response.roles
      ? response.roles
          .split(',')
          .map((role) => role.trim())
          .filter(Boolean)
      : [],
  };

  return transformedData;
}

export async function getUserDetailApi() {
  return requestClient.get<UserInfo>('/user/detail');
}
