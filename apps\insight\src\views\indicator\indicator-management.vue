<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type {
  OnActionClickParams,
  VxeGridProps,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Popconfirm } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteIndicatorCategory,
  deleteIndicatorConfig,
  getIndicatorCategoriesWithConfigs,
  IndicatorApi,
} from '#/api/indicator';

import IndicatorCategoryForm from './indicator-category-form.vue';
import IndicatorForm from './indicator-form.vue';

// Modal 组件定义
const [IndicatorFormModal, IndicatorFormModalApi] = useVbenModal({
  connectedComponent: IndicatorForm,
});

const [IndicatorCategoryFormModal, IndicatorCategoryFormModalApi] =
  useVbenModal({
    connectedComponent: IndicatorCategoryForm,
  });

// 搜索表单配置
const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '分类名称',
      },
      fieldName: 'name',
      label: '分类名称',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '级别',
        options: [
          { label: '一级分类', value: 1 },
          { label: '二级分类', value: 2 },
        ],
      },
      fieldName: 'level',
      label: '级别',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '状态',
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
      fieldName: 'status',
      label: '状态',
    },
  ],
  showDefaultActions: true,
  showCollapseButton: false,
  submitButtonOptions: {
    text: '搜索',
  },
  resetButtonOptions: {
    text: '重置',
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
};

// 分类操作处理
function onCategoryActionClick({
  code,
  row,
}: OnActionClickParams<IndicatorApi.IndicatorCategory>) {
  switch (code) {
    case 'addIndicator': {
      onIndicatorCreate(row);
      break;
    }
    case 'delete': {
      onCategoryDelete(row);
      break;
    }
    case 'edit': {
      onCategoryEdit(row);
      break;
    }
  }
}

// 分类管理函数
function onCategoryCreate() {
  IndicatorCategoryFormModalApi.setData(null).open();
}

function onCategoryEdit(row: IndicatorApi.IndicatorCategory) {
  IndicatorCategoryFormModalApi.setData(row).open();
}

async function onCategoryDelete(row: IndicatorApi.IndicatorCategory) {
  try {
    await deleteIndicatorCategory(row.id);
    message.success('删除成功');
    refreshGrid();
  } catch (error) {
    console.error('删除指标分类失败:', error);
    message.error('删除指标分类失败');
  }
}

// 指标管理函数
function onIndicatorCreate(category?: IndicatorApi.IndicatorCategory) {
  const data = category
    ? {
        class1: category.id,
        class2: null,
        status: 1,
      }
    : {
        class1: null,
        class2: null,
        status: 1,
      };
  IndicatorFormModalApi.setData(data).open();
}

function onIndicatorEdit(row: IndicatorApi.IndicatorConfig) {
  IndicatorFormModalApi.setData(row).open();
}

async function onIndicatorDelete(row: IndicatorApi.IndicatorConfig) {
  try {
    await deleteIndicatorConfig(row.id);
    message.success('删除成功');
    refreshGrid();
  } catch (error) {
    console.error('删除指标配置失败:', error);
    message.error('删除指标配置失败');
  }
}

function refreshGrid() {
  gridApi.query();
}

// 主表格配置（分类列表）
const gridOptions: VxeGridProps = {
  columns: [
    { type: 'expand', width: 35, slots: { content: 'expand_content' } },
    { title: 'ID', field: 'id', width: 80, align: 'center', visible: false },
    {
      title: '分类名称',
      field: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: '级别',
      field: 'level',
      width: 100,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          {
            label: '一级分类',
            value: 1,
            color: 'processing',
          },
          {
            label: '二级分类',
            value: 2,
            color: 'success',
          },
        ],
      },
    },
    {
      title: '描述',
      field: 'description',
      align: 'left',
      showOverflow: 'tooltip',
      cellRender: {
        name: 'CellText',
        options: {
          format: (value: string) => value || '-',
        },
      },
    },
    {
      title: '指标数量',
      field: 'indicators.length',
      width: 100,
      align: 'center',
      cellRender: {
        name: 'CellText',
        options: {
          format: (value: number) => value || 0,
        },
      },
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 160,
      align: 'center',
      cellRender: {
        name: 'CellText',
        options: {
          format: (value: string) => {
            return value ? new Date(value).toLocaleString() : '-';
          },
        },
      },
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          {
            label: '启用',
            value: 1,
            color: 'success',
          },
          {
            label: '禁用',
            value: 0,
            color: 'error',
          },
        ],
      },
    },
    {
      title: '操作',
      field: 'operation',
      width: 200,
      align: 'center',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      cellRender: {
        attrs: {
          nameField: '',
          nameTitle: '',
          onClick: onCategoryActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'addIndicator',
            text: '新增指标',
            props: {
              type: 'primary',
              size: 'small',
            },
          },
          {
            code: 'edit',
            text: '编辑',
          },
          {
            code: 'delete',
            text: '删除',
            props: {
              type: 'danger',
            },
            confirm: {
              title: '确定要删除这个指标分类吗？',
            },
          },
        ],
      },
    },
  ],
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: true,
    pageSize: 10,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          const response: any = await getIndicatorCategoriesWithConfigs({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });

          // 直接使用API处理后的数据
          const records = response.records || [];
          const total = response.total || 0;

          // 处理字段名映射：indicatorConfig -> indicators
          const processedRecords = records.map((category: any) => ({
            ...category,
            indicators: category.indicatorConfig || category.indicators || [],
          }));

          return {
            records: processedRecords,
            total,
          };
        } catch (error) {
          console.error('获取指标分类数据失败:', error);
          return {
            records: [],
            total: 0,
          };
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
} as VxeTableGridOptions;

// 子表格配置（指标配置列表）
const subGridOptions: VxeGridProps = {
  columns: [
    { title: 'ID', field: 'id', visible: false },
    { title: '指标名称', field: 'indicatorName', width: 200, align: 'center' },
    { title: '指标编码', field: 'indicatorCode', width: 160, align: 'center' },
    {
      title: '数据类型',
      field: 'returnType',
      width: 100,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          {
            label: 'Bigdecimal',
            value: 0,
            color: 'processing',
          },
          {
            label: 'Object',
            value: 1,
            color: 'success',
          },
          {
            label: 'List',
            value: 2,
            color: 'warning',
          },
        ],
      },
    },
    {
      title: '执行SQL',
      field: 'executeSql',
      width: 200,
      align: 'left',
      showOverflow: 'tooltip',
    },
    {
      title: '描述',
      field: 'description',
      width: 180,
      align: 'left',
      showOverflow: 'tooltip',
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 160,
      align: 'center',
      cellRender: {
        name: 'CellText',
        options: {
          format: (value: string) => {
            return value ? new Date(value).toLocaleString() : '-';
          },
        },
      },
    },
    {
      title: '更新时间',
      field: 'updatedAt',
      width: 160,
      align: 'center',
      cellRender: {
        name: 'CellText',
        options: {
          format: (value: string) => {
            return value ? new Date(value).toLocaleString() : '-';
          },
        },
      },
    },
    {
      title: '状态',
      field: 'status',
      width: 80,
      align: 'center',
      cellRender: {
        name: 'CellTag',
        options: [
          {
            label: '启用',
            value: 1,
            color: 'success',
          },
          {
            label: '禁用',
            value: 0,
            color: 'error',
          },
        ],
      },
    },
    {
      field: 'operation',
      align: 'right',
      fixed: 'right',
      showOverflow: false,
      slots: { default: 'operation' },
    },
  ],
  pagerConfig: {
    enabled: false,
  },
  minHeight: 1,
  border: false,
} as VxeTableGridOptions;

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [SubGrid] = useVbenVxeGrid({ gridOptions: subGridOptions });
</script>

<template>
  <Page auto-content-height>
    <IndicatorFormModal @success="refreshGrid" />
    <IndicatorCategoryFormModal @success="refreshGrid" />
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="onCategoryCreate">
          <Plus class="size-5" />
          新增指标分类
        </Button>
      </template>
      <template #expand_content="{ row }">
        <SubGrid :grid-options="{ ...subGridOptions, data: row.indicators }">
          <template #operation="{ row: indicator }">
            <div class="flex items-center justify-end gap-2">
              <Button
                type="link"
                size="small"
                @click="onIndicatorEdit(indicator)"
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个指标配置吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="onIndicatorDelete(indicator)"
              >
                <Button type="link" size="small" danger> 删除 </Button>
              </Popconfirm>
            </div>
          </template>
        </SubGrid>
      </template>
    </Grid>
  </Page>
</template>
