<script lang="ts" setup>
import type { SelectValue } from 'ant-design-vue/es/select';

import { computed, ref, watch } from 'vue';

import { Select, Table, Typography } from 'ant-design-vue';

import { MENU_KEYS } from '../config/report-menu-config';

interface TaxAnalysisData {
  corporateIncomeTax?: any[];
  vat?: any[];
  propertyTax?: any[];
  landUseTax?: any[];
  consumptionTax?: any[];
  stampDuty?: any[];
  urbanMaintenanceTax?: any[];
  educationSurcharge?: any[];
  landAppreciationTax?: any[];
  resourceTax?: any[];
  individualIncomeTax?: any[];
  socialInsurance?: any[];
}

interface Props {
  taxAnalysisData: TaxAnalysisData;
}

const props = defineProps<Props>();

const { Title } = Typography;
// 年份选择
const availableYears = computed(() => {
  const years = new Set<number>();

  // 从所有税种数据中提取年份
  Object.values(props.taxAnalysisData || {}).forEach((taxArray: any[]) => {
    if (Array.isArray(taxArray)) {
      taxArray.forEach((item: any) => {
        if (item.year && typeof item.year === 'number') {
          years.add(item.year);
        }
      });
    }
  });

  return [...years].sort((a, b) => b - a);
});

const selectedYear = ref<number | undefined>(undefined);

// 初始化选中年份
watch(
  availableYears,
  (years) => {
    if (years.length > 0 && !selectedYear.value) {
      selectedYear.value = years[0];
    }
  },
  { immediate: true },
);

// 年份选择处理
const handleYearChange = (year: SelectValue) => {
  selectedYear.value = year as number;
};

// 格式化数字显示
const formatAmount = (value: null | number | undefined): string => {
  if (value === undefined || value === null) return '--';
  return `${value} 元`;
};

// 格式化百分比显示
const formatPercentage = (value: null | number | undefined): string => {
  if (value === undefined || value === null) return '--';
  return `${(value * 100).toFixed(2)}%`;
};

// 获取比较描述
const getComparisonText = (current: number, industry: number): string => {
  if (current > industry) return '高于';
  if (current < industry) return '低于';
  return '等于';
};

// 生成所得税税负率描述
const getIncomeTaxBurdenDescription = computed(() => {
  const data = currentYearData.value.corporateIncomeTax;
  if (!data || !selectedYear.value) return '';

  const currentBurden = data.incomeTaxBurden;
  const industryBurden = data.industryIncomeTaxBurden;

  if (currentBurden === undefined || industryBurden === undefined) return '';

  const comparison = getComparisonText(currentBurden, industryBurden);

  return `当前企业${selectedYear.value}年度所得税税负率：${formatPercentage(currentBurden)}，${comparison}同行业同规模的行业平均所得税税负率：${formatPercentage(industryBurden)}`;
});

// 生成增值税税负率描述
const getVatTaxBurdenDescription = computed(() => {
  const data = currentYearData.value.vat;
  if (!data || !selectedYear.value) return '';

  const vatBurden = data.vatBurden;
  const addTaxBurden = data.addTaxBurden;
  const industryVatBurden = data.industryVatTaxBurden;

  if (vatBurden === undefined || industryVatBurden === undefined) return '';

  const comparison = getComparisonText(vatBurden, industryVatBurden);
  let description = `当前企业${selectedYear.value}年度增值税负率：${formatPercentage(vatBurden)}，${comparison}同行业同规模的行业平均增值税负率：${formatPercentage(industryVatBurden)}`;

  if (addTaxBurden !== undefined) {
    description += `，增加值税负率为：${formatPercentage(addTaxBurden)}`;
  }

  return description;
});

// 获取指定年份的税种数据
const getTaxDataByYear = (taxArray: any[], year: number) => {
  return taxArray?.find((item: any) => item.year === year) || {};
};

// 当前年份的各税种数据
const currentYearData = computed(() => {
  if (!selectedYear.value || !props.taxAnalysisData) return {};

  return {
    corporateIncomeTax: getTaxDataByYear(
      props.taxAnalysisData.corporateIncomeTax || [],
      selectedYear.value,
    ),
    vat: getTaxDataByYear(props.taxAnalysisData.vat || [], selectedYear.value),
    propertyTax: getTaxDataByYear(
      props.taxAnalysisData.propertyTax || [],
      selectedYear.value,
    ),
    landUseTax: getTaxDataByYear(
      props.taxAnalysisData.landUseTax || [],
      selectedYear.value,
    ),
    consumptionTax: getTaxDataByYear(
      props.taxAnalysisData.consumptionTax || [],
      selectedYear.value,
    ),
    stampDuty: getTaxDataByYear(
      props.taxAnalysisData.stampDuty || [],
      selectedYear.value,
    ),
    urbanMaintenanceTax: getTaxDataByYear(
      props.taxAnalysisData.urbanMaintenanceTax || [],
      selectedYear.value,
    ),
    educationSurcharge: getTaxDataByYear(
      props.taxAnalysisData.educationSurcharge || [],
      selectedYear.value,
    ),
    individualIncomeTax:
      (props.taxAnalysisData.individualIncomeTax || [])[0] || {},
    socialInsurance: (props.taxAnalysisData.socialInsurance || [])[0] || {},
  };
});

// 企业所得税表格数据
const incomeTaxTableData = computed(() => {
  const data = currentYearData.value.corporateIncomeTax;

  return [
    // 收入调增事项
    {
      key: 'income-increase-category',
      category: '收入调增事项',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'income-increase-1',
      category: '',
      item: '自产产品、财产用于交换、捐赠、偿债、赞助、集资、广告、样品、职工福利视同销售收入',
      amount: formatAmount(data?.incomeAdjustments?.deemedSalesIncome),
      isCategoryRow: false,
    },
    {
      key: 'income-increase-2',
      category: '',
      item: '下脚料、包装物未确认收入',
      amount: formatAmount(data?.incomeAdjustments?.unrecordedScrapIncome),
      isCategoryRow: false,
    },
    {
      key: 'income-increase-3',
      category: '',
      item: '未按权责发生制确认收到的租金、利息等收入',
      amount: formatAmount(
        data?.incomeAdjustments?.unrecordedRentInterestIncome,
      ),
      isCategoryRow: false,
    },
    {
      key: 'income-increase-4',
      category: '',
      item: '预收账款未及时确认收入',
      amount: formatAmount(data?.incomeAdjustments?.unrecordedAdvanceReceipts),
      isCategoryRow: false,
    },
    {
      key: 'income-increase-5',
      category: '',
      item: '资金占用视同收入',
      amount: formatAmount(data?.incomeAdjustments?.deemedInterestIncome),
      isCategoryRow: false,
    },
    // 收入调减事项
    {
      key: 'income-decrease-category',
      category: '收入调减事项',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'income-decrease-1',
      category: '',
      item: '财政拨款、依法收取的行政事业性收费、政府性基金等不征税收入',
      amount: formatAmount(data?.incomeDeductions?.nonTaxableGovIncome),
      isCategoryRow: false,
    },
    {
      key: 'income-decrease-2',
      category: '',
      item: '国债利息免税收入',
      amount: formatAmount(data?.incomeDeductions?.taxExemptBondInterest),
      isCategoryRow: false,
    },
    {
      key: 'income-decrease-3',
      category: '',
      item: '从直接投资于其他居民企业分回的股息、红利等免税收入',
      amount: formatAmount(data?.incomeDeductions?.taxExemptDividends),
      isCategoryRow: false,
    },
    {
      key: 'income-decrease-4',
      category: '',
      item: '符合条件的资源综合利用减计收入',
      amount: formatAmount(
        data?.incomeDeductions?.resourceUtilizationDeduction,
      ),
      isCategoryRow: false,
    },
    {
      key: 'income-decrease-5',
      category: '',
      item: '以前期间销售产生的本期销售退回、折扣、折让',
      amount: formatAmount(data?.incomeDeductions?.priorPeriodReturns),
      isCategoryRow: false,
    },
    {
      key: 'income-decrease-6',
      category: '',
      item: '以前期间错误多计收入在本期调整',
      amount: formatAmount(data?.incomeDeductions?.priorPeriodOverstatedIncome),
      isCategoryRow: false,
    },
    // 扣除类调整事项（调增项）
    {
      key: 'deduction-increase-category',
      category: '扣除类调整事项（调增项）',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'deduction-increase-1',
      category: '',
      item: '自产产品、财产用于交换、捐赠、偿债、赞助、集资、广告、样品、职工福利视同销售成本',
      amount: formatAmount(data?.expenseAdjustments?.deemedSalesCost),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-2',
      category: '',
      item: '职工薪酬不得扣除金额',
      amount: formatAmount(data?.expenseAdjustments?.nonDeductibleSalaries),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-3',
      category: '',
      item: '职工福利费超限额列支',
      amount: formatAmount(data?.expenseAdjustments?.excessWelfare),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-4',
      category: '',
      item: '工会经费超限额列支',
      amount: formatAmount(data?.expenseAdjustments?.excessUnionFees),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-5',
      category: '',
      item: '职工教育经费超限额列支',
      amount: formatAmount(data?.expenseAdjustments?.excessTrainingFees),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-6',
      category: '',
      item: '业务招待费超限额列支',
      amount: formatAmount(data?.expenseAdjustments?.excessEntertainment),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-7',
      category: '',
      item: '广告和业务宣传费超限额列支',
      amount: formatAmount(data?.expenseAdjustments?.excessAdvertising),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-8',
      category: '',
      item: '公益性捐赠支出超限额列支',
      amount: formatAmount(data?.expenseAdjustments?.excessDonations),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-9',
      category: '',
      item: '非公益性捐赠支出',
      amount: formatAmount(data?.expenseAdjustments?.nonCharitableDonations),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-10',
      category: '',
      item: '向非金融企业和关联方借款利息支出不得扣除金额',
      amount: formatAmount(data?.expenseAdjustments?.nonDeductibleInterest),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-11',
      category: '',
      item: '不允许列支的罚款、罚金、滞纳金',
      amount: formatAmount(data?.expenseAdjustments?.nonDeductiblePenalties),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-12',
      category: '',
      item: '手续费和佣金超额不得扣除金额',
      amount: formatAmount(data?.expenseAdjustments?.excessCommission),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-13',
      category: '',
      item: '与经营活动无关的赞助支出',
      amount: formatAmount(data?.expenseAdjustments?.nonBusinessSponsorship),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-14',
      category: '',
      item: '未经核定的准备金支出',
      amount: formatAmount(data?.expenseAdjustments?.unapprovedReserves),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-15',
      category: '',
      item: '安置残疾人员工资加计扣除金额',
      amount: formatAmount(data?.expenseAdjustments?.disabledStaffDeduction),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-16',
      category: '',
      item: '环保、节能节水、安全生产专用设备投资额抵免应纳税所得额',
      amount: formatAmount(data?.expenseAdjustments?.ecoEquipmentDeduction),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-17',
      category: '',
      item: '创业投资企业投资额70%抵免应纳税所得额',
      amount: formatAmount(
        data?.expenseAdjustments?.ventureInvestmentDeduction,
      ),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-18',
      category: '',
      item: '不征税收入用于支出形成的费用',
      amount: formatAmount(data?.expenseAdjustments?.nonTaxableExpenses),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-19',
      category: '',
      item: '与取得收入无关的支出',
      amount: formatAmount(data?.expenseAdjustments?.irrelevantExpenses),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-20',
      category: '',
      item: '暂估成本尚未取得发票',
      amount: formatAmount(data?.expenseAdjustments?.uninvoicedCosts),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-21',
      category: '',
      item: '未按会计核算制度，多结转成本',
      amount: formatAmount(data?.expenseAdjustments?.overstatedCosts),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-22',
      category: '',
      item: '白条支出的成本费用',
      amount: formatAmount(data?.expenseAdjustments?.unrecordedCosts),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-23',
      category: '',
      item: '库存商品负数',
      amount: formatAmount(data?.expenseAdjustments?.negativeInventory),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-24',
      category: '',
      item: '改变库存商品计价方式多结转',
      amount: formatAmount(
        data?.expenseAdjustments?.inventoryValuationAdjustment,
      ),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-25',
      category: '',
      item: '原材料负数，此项并不必然导致多结转成本',
      amount: formatAmount(data?.expenseAdjustments?.negativeMaterials),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-26',
      category: '',
      item: '生产成本,此项并不必然导致多结转成本',
      amount: formatAmount(data?.expenseAdjustments?.productionCosts),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-27',
      category: '',
      item: '未按会计核算制度，结转和销项不匹配的成本',
      amount: formatAmount(data?.expenseAdjustments?.mismatchedInventoryCosts),
      isCategoryRow: false,
    },
    {
      key: 'deduction-increase-28',
      category: '',
      item: '应由个人负担的社会保险费',
      amount: formatAmount(data?.expenseAdjustments?.personalSocialInsurance),
      isCategoryRow: false,
    },
    // 扣除类调整事项（调减项）
    {
      key: 'deduction-decrease-category',
      category: '扣除类调整事项（调减项）',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'deduction-decrease-1',
      category: '',
      item: '研发费用加计扣除金额',
      amount: formatAmount(data?.expenseDeductions?.rdDeduction),
      isCategoryRow: false,
    },
    {
      key: 'deduction-decrease-2',
      category: '',
      item: '跨期入账的成本费用',
      amount: formatAmount(data?.expenseDeductions?.crossPeriodCosts),
      isCategoryRow: false,
    },
    {
      key: 'deduction-decrease-3',
      category: '',
      item: '取得单一来源小规模成本发票，应调整',
      amount: formatAmount(
        data?.expenseDeductions?.smallScaleInvoiceAdjustment,
      ),
      isCategoryRow: false,
    },
    // 资产类调整事项
    {
      key: 'asset-category',
      category: '资产类调整事项',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'asset-1',
      category: '',
      item: '资产折旧、摊销调整额',
      amount: formatAmount(data?.assetAdjustments?.depreciationAdjustment),
      isCategoryRow: false,
    },
    {
      key: 'asset-2',
      category: '',
      item: '不允许列支的资产准备金',
      amount: formatAmount(data?.assetAdjustments?.nonDeductibleReserves),
      isCategoryRow: false,
    },
    {
      key: 'asset-3',
      category: '',
      item: '各项资产损失纳税调整金额',
      amount: formatAmount(data?.assetAdjustments?.assetLossAdjustment),
      isCategoryRow: false,
    },
    // 特殊事项调整项目
    {
      key: 'special-category',
      category: '特殊事项调整项目',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'special-1',
      category: '',
      item: '企业重组及递延纳税金额',
      amount: formatAmount(data?.specialAdjustments?.reorganizationDeferral),
      isCategoryRow: false,
    },
    {
      key: 'special-2',
      category: '',
      item: '政策性搬迁递延纳税金额',
      amount: formatAmount(data?.specialAdjustments?.relocationDeferral),
      isCategoryRow: false,
    },
  ];
});

// 增值税表格数据
const vatTableData = computed(() => {
  const data = currentYearData.value.vat;

  return [
    // 销项税额方面
    {
      key: 'output-tax-category',
      category: '销项税额方面',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'output-tax-1',
      category: '',
      item: '预收账款未确认收入少申报税款',
      amount: formatAmount(data?.outputTax?.unrecordedAdvanceReceipts),
      isCategoryRow: false,
    },
    {
      key: 'output-tax-2',
      category: '',
      item: '下脚料包装物收入未确认收入少申报纳税',
      amount: formatAmount(data?.outputTax?.unrecordedScrapIncome),
      isCategoryRow: false,
    },
    {
      key: 'output-tax-3',
      category: '',
      item: '视同销售未按规定申报纳税',
      amount: formatAmount(data?.outputTax?.unrecordedDeemedSales),
      isCategoryRow: false,
    },
    {
      key: 'output-tax-4',
      category: '',
      item: '价外收入未按规定并入销售额申报纳税',
      amount: formatAmount(data?.outputTax?.unrecordedSurcharges),
      isCategoryRow: false,
    },
    {
      key: 'output-tax-5',
      category: '',
      item: '销售折扣折让处理不当少申报税款',
      amount: formatAmount(data?.outputTax?.incorrectDiscounts),
      isCategoryRow: false,
    },
    {
      key: 'output-tax-6',
      category: '',
      item: '混合销售行为税率选择不当少申报纳税',
      amount: formatAmount(data?.outputTax?.incorrectTaxRate),
      isCategoryRow: false,
    },
    {
      key: 'output-tax-7',
      category: '',
      item: '资金占用视同销售少申报纳税',
      amount: formatAmount(data?.outputTax?.deemedInterestSales),
      isCategoryRow: false,
    },
    // 进项税额方面
    {
      key: 'input-tax-category',
      category: '进项税额方面',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'input-tax-1',
      category: '',
      item: '取得不合规发票抵扣进项税额',
      amount: formatAmount(data?.inputTax?.nonCompliantInvoices),
      isCategoryRow: false,
    },
    {
      key: 'input-tax-2',
      category: '',
      item: '用于不得抵扣项目未转出进项税额',
      amount: formatAmount(data?.inputTax?.nonDeductibleInputs),
      isCategoryRow: false,
    },
    {
      key: 'input-tax-3',
      category: '',
      item: '非正常损失未作进项税额转出',
      amount: formatAmount(data?.inputTax?.abnormalLoss),
      isCategoryRow: false,
    },
    {
      key: 'input-tax-4',
      category: '',
      item: '抵扣了特殊项目（4项）进项税额',
      amount: formatAmount(data?.inputTax?.specialItemDeductions),
      isCategoryRow: false,
    },
    // 税率及其他
    {
      key: 'other-vat-category',
      category: '税率及其他',
      item: '',
      amount: '',
      isCategoryRow: true,
    },
    {
      key: 'other-vat-1',
      category: '',
      item: '高税率项目应用低税率项目少申报纳税',
      amount: formatAmount(data?.otherVat?.understatedHighRateItems),
      isCategoryRow: false,
    },
    {
      key: 'other-vat-2',
      category: '',
      item: '误用简易计税办法少申报税额',
      amount: formatAmount(data?.otherVat?.incorrectSimplifiedMethod),
      isCategoryRow: false,
    },
    {
      key: 'other-vat-3',
      category: '',
      item: '实际税负低于增加值税负率',
      amount: formatAmount(data?.otherVat?.lowTaxBurden),
      isCategoryRow: false,
    },
    {
      key: 'other-vat-4',
      category: '',
      item: '按行业平均能耗预估少计收入',
      amount: formatAmount(data?.otherVat?.energyBasedEstimate),
      isCategoryRow: false,
    },
  ];
});

// 房产税表格数据
const propertyTaxTableData = computed(() => {
  const data = currentYearData.value.propertyTax;

  return [
    {
      key: 'property-tax-1',
      category: '',
      item: '土地原值、改扩建费用未计入房产原值而少申报税款',
      amount: formatAmount(data?.landValueOmission),
      isCategoryRow: false,
    },
    {
      key: 'property-tax-2',
      category: '',
      item: '从租从价计算错误而少申报税款',
      amount: formatAmount(data?.calculationError),
      isCategoryRow: false,
    },
    {
      key: 'property-tax-3',
      category: '',
      item: '无偿使用关联方房产未代扣代缴税款',
      amount: formatAmount(data?.unpaidRelatedPartyRent),
      isCategoryRow: false,
    },
  ];
});

// 城镇土地使用税表格数据
const landUseTaxTableData = computed(() => {
  const data = currentYearData.value.landUseTax;

  return [
    {
      key: 'land-use-tax-1',
      category: '',
      item: '误用土地等级税额标准少计算交纳税款',
      amount: formatAmount(data?.incorrectLandGradeRate),
      isCategoryRow: false,
    },
    {
      key: 'land-use-tax-2',
      category: '',
      item: '误用土地使用面积少计算交纳税款',
      amount: formatAmount(data?.incorrectLandArea),
      isCategoryRow: false,
    },
  ];
});

// 消费税表格数据
const consumptionTaxTableData = computed(() => {
  const data = currentYearData.value.consumptionTax;

  return [
    {
      key: 'consumption-tax-1',
      category: '',
      item: '税率引用错误少申报税款',
      amount: formatAmount(data?.incorrectTaxRate),
      isCategoryRow: false,
    },
    {
      key: 'consumption-tax-2',
      category: '',
      item: '赠品未视同销售少申报税款',
      amount: formatAmount(data?.unrecordedGifts),
      isCategoryRow: false,
    },
  ];
});

// 印花税表格数据
const stampDutyTableData = computed(() => {
  const data = currentYearData.value.stampDuty;

  return [
    {
      key: 'stamp-duty-1',
      category: '',
      item: '税目税率引用错误少申报税款',
      amount: formatAmount(data?.incorrectTaxCategory),
      isCategoryRow: false,
    },
    {
      key: 'stamp-duty-2',
      category: '',
      item: '注册资本与资本公积增加少申报税款',
      amount: formatAmount(data?.unrecordedCapitalIncrease),
      isCategoryRow: false,
    },
    {
      key: 'stamp-duty-3',
      category: '',
      item: '股权变动事项少申报税款',
      amount: formatAmount(data?.unrecordedEquityChanges),
      isCategoryRow: false,
    },
    {
      key: 'stamp-duty-4',
      category: '',
      item: '因查补的隐匿收入少申报税款',
      amount: formatAmount(data?.unrecordedHiddenIncome),
      isCategoryRow: false,
    },
  ];
});

// 城建税表格数据
const urbanMaintenanceTaxTableData = computed(() => {
  const data = currentYearData.value.urbanMaintenanceTax;

  return [
    {
      key: 'urban-maintenance-tax-1',
      category: '',
      item: '出口企业免抵增值税额未申报相应税费',
      amount: formatAmount(data?.unrecordedExportCredit),
      isCategoryRow: false,
    },
    {
      key: 'urban-maintenance-tax-2',
      category: '',
      item: '按主税种申报应纳税额预估的少缴税费',
      amount: formatAmount(data?.estimatedUnderpayment),
      isCategoryRow: false,
    },
    {
      key: 'urban-maintenance-tax-3',
      category: '',
      item: '因查补的隐匿收入少申报税款',
      amount: formatAmount(data?.unrecordedHiddenIncome),
      isCategoryRow: false,
    },
  ];
});

// 教育费附加表格数据
const educationSurchargeTableData = computed(() => {
  const data = currentYearData.value.educationSurcharge;

  return [
    {
      key: 'education-surcharge-1',
      category: '',
      item: '出口企业免抵增值税额未申报相应税费',
      amount: formatAmount(data?.unrecordedExportCredit),
      isCategoryRow: false,
    },
    {
      key: 'education-surcharge-2',
      category: '',
      item: '按主税种申报应纳税额预估的少缴税费',
      amount: formatAmount(data?.estimatedUnderpayment),
      isCategoryRow: false,
    },
    {
      key: 'education-surcharge-3',
      category: '',
      item: '因查补的隐匿收入少申报税款',
      amount: formatAmount(data?.unrecordedHiddenIncome),
      isCategoryRow: false,
    },
  ];
});

// 个人所得税表格数据
const individualIncomeTaxTableData = computed(() => {
  const data = currentYearData.value.individualIncomeTax;

  return [
    {
      key: 'individual-tax-1',
      category: '',
      item: '个人代开的劳务报酬未代扣代缴税款',
      amount: formatAmount(data?.unwithheldLaborIncome),
      isCategoryRow: false,
    },
    {
      key: 'individual-tax-2',
      category: '',
      item: '股东其他应收款挂账被视同分红未代扣代缴',
      amount: `${formatAmount(data?.deemedDividends)}`,
      isCategoryRow: false,
    },
    {
      key: 'individual-tax-3',
      category: '',
      item: '对外赠送被视同销售未代扣代缴',
      amount: `${formatAmount(data?.deemedGiftSales)}`,
      isCategoryRow: false,
    },
    {
      key: 'individual-tax-4',
      category: '',
      item: '高管薪资收入分拆应代扣代缴税款',
      amount: formatAmount(data?.splitExecutiveSalaries),
      isCategoryRow: false,
    },
  ];
});

// 社会保险金表格数据
const socialInsuranceTableData = computed(() => {
  const data = currentYearData.value.socialInsurance;

  return [
    {
      key: 'social-insurance-1',
      category: '',
      item: '未足额缴纳社会保险金',
      amount: formatAmount(data?.insufficientSocialInsurance),
      isCategoryRow: false,
    },
  ];
});

// 表格列定义
const tableColumns = [
  {
    title: '项目',
    dataIndex: 'item',
    key: 'item',
    width: '70%',
    align: 'left' as const,
    customRender: ({ record }: { record: any }) => {
      if (record.isCategoryRow && record.category) {
        return record.category;
      }
      return record.item || '';
    },
    customCell: (record: any) => ({
      style: {
        backgroundColor: '#f4f8ff',
        fontWeight: record.isCategoryRow ? 'bold' : 'normal',
        padding: '8px 12px',
        color: 'black',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
  {
    title: '指标数据',
    dataIndex: 'amount',
    key: 'amount',
    width: '30%',
    align: 'center' as const,
    customRender: ({ record }: { record: any }) => {
      if (record.isCategoryRow) {
        return '';
      }
      return record.amount || '';
    },
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
        color: 'black',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
];
</script>

<template>
  <div class="w-full">
    <!-- 年份选择器 -->
    <div class="mb-4 flex justify-end">
      <Select
        v-model:value="selectedYear"
        style="width: 120px"
        placeholder="选择年份"
        @change="handleYearChange"
      >
        <Select.Option v-for="year in availableYears" :key="year" :value="year">
          {{ year }}年
        </Select.Option>
      </Select>
    </div>

    <!-- 企业所得税 -->
    <div :id="MENU_KEYS.ENTERPRISE_INCOME_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 1.企业所得税 </Title>

      <!-- 税负率描述 -->
      <div
        v-if="getIncomeTaxBurdenDescription"
        class="mb-4 rounded-lg bg-green-50 p-4"
      >
        <p class="mb-0 text-gray-700">{{ getIncomeTaxBurdenDescription }}</p>
      </div>

      <Table
        :columns="tableColumns"
        :data-source="incomeTaxTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 增值税 -->
    <div :id="MENU_KEYS.VALUE_ADDED_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 2.增值税 </Title>

      <!-- 税负率描述 -->
      <div
        v-if="getVatTaxBurdenDescription"
        class="mb-4 rounded-lg bg-green-50 p-4"
      >
        <p class="mb-0 text-gray-700">{{ getVatTaxBurdenDescription }}</p>
      </div>

      <Table
        :columns="tableColumns"
        :data-source="vatTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
      <div class="py-8">销项税额方面:</div>
      <div class="text-center">开具红字发票清单如下</div>

      <div class="py-8">进项税额方面:</div>
      <div class="">取得不合规发票抵扣进项税清单:</div>
      <div class="text-center">对方虚开发票受处理的发票清单如下</div>
      <div class="text-center">一年以上挂账未支付的发票清单如下</div>
      <div class="text-center">中文名经检索和税目不一致的发票清单如下</div>
      <div class="text-center">一年以上不使用的存货对应的发票清单如下</div>
      <div class="text-center">
        和销项不匹配，已结转成本的存货对应的发票清单如下
      </div>
      <div class="text-center">用于不得抵扣项目未转出进项税清单如下</div>

      <div class="py-8">税率及其他:</div>
      <div class="text-center">开具零税率发票列表如下</div>
      <div class="text-center">开具免税发票列表如下</div>
    </div>

    <!-- 房产税 -->
    <div :id="MENU_KEYS.PROPERTY_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 3.房产税 </Title>
      <Table
        :columns="tableColumns"
        :data-source="propertyTaxTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 城镇土地使用税 -->
    <div :id="MENU_KEYS.URBAN_LAND_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 4.城镇土地使用税 </Title>
      <Table
        :columns="tableColumns"
        :data-source="landUseTaxTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 消费税 -->
    <div :id="MENU_KEYS.CONSUMPTION_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 5.消费税 </Title>
      <Table
        :columns="tableColumns"
        :data-source="consumptionTaxTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 印花税 -->
    <div :id="MENU_KEYS.STAMP_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 6.印花税 </Title>
      <Table
        :columns="tableColumns"
        :data-source="stampDutyTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 城建税 -->
    <div :id="MENU_KEYS.URBAN_CONSTRUCTION_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 7.城建税 </Title>
      <Table
        :columns="tableColumns"
        :data-source="urbanMaintenanceTaxTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 教育费附加 -->
    <div :id="MENU_KEYS.EDUCATION_EXPENSE" class="mb-6">
      <Title :level="4" class="mb-4"> 8.教育费附加及地方教育费附加 </Title>
      <Table
        :columns="tableColumns"
        :data-source="educationSurchargeTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>

    <!-- 土地增值税 -->
    <div :id="MENU_KEYS.LAND_APPRECIATION_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 9.土地增值税 </Title>
      <div class="py-8 text-center text-gray-500">暂无数据</div>
    </div>

    <!-- 资源税 -->
    <div :id="MENU_KEYS.RESOURCE_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 10.资源税 </Title>
      <div class="py-8 text-center text-gray-500">暂无数据</div>
    </div>

    <!-- 个人所得税 -->
    <div :id="MENU_KEYS.PERSONAL_INCOME_TAX" class="mb-6">
      <Title :level="4" class="mb-4"> 11.个人所得税 </Title>
      <Table
        :columns="tableColumns"
        :data-source="individualIncomeTaxTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />

      <!-- 固定资产折旧特别提醒 -->
      <div class="mt-4 rounded border border-orange-300 bg-orange-50 p-3">
        <span class="font-semibold text-orange-700">特别提醒：</span>
        <span class="text-orange-700">
          因固定资产没有合法凭证等原因没有严格按财务制度计提折旧而导致可能虚增税后利润
          {{
            formatAmount(currentYearData.individualIncomeTax?.specialNotes)
          }}， 而导致股转变更功注销时增加税负！
        </span>
      </div>
    </div>

    <!-- 社会保险金 -->
    <div :id="MENU_KEYS.SOCIAL_INSURANCE" class="mb-6">
      <Title :level="4" class="mb-4"> 12.社会保险金 </Title>
      <Table
        :columns="tableColumns"
        :data-source="socialInsuranceTableData"
        :pagination="false"
        size="small"
        bordered
        :show-header="true"
        class="w-full"
      />
    </div>
  </div>
</template>
