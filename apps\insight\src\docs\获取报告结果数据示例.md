

### 发票分析目录结构

1. 上下游关系网络分析

  网状结构展示,上下游数据如下

  上游风险企业 `invoiceAnalysis.purchaseInvoice.invoiceRiskStats`
  下游风险企业 `invoiceAnalysis.salesInvoice.invoiceRiskStats`

2. 进项发票分析
    1. 取得不合规发票抵扣进项税清单
       1. 对方虚开发票受处理的发票清单 `invoiceAnalysis.purchaseInvoice.nonCompliantInvoiceList.fraudulentInvoices`
       2. 一年以上挂账未支付的发票清单
       3. 中文名经检索和税目不一致的发票清单
       4. 一年以上不使用的存货对应的发票清单
       5. 和销项不匹配，已结转成本的存货对应的发票清单
      2. 用于不得抵扣项目未转出进项税清单 
3. 销项发票分析

   1. 开具红字发票清单
4. 进项销项金额（年度）分析
     风险项取值:  `invoiceAnalysis`
     进项不含税金额、销项不含税金额对比分析 `invoiceAnalysis.taxExemptComparisons`
5. 发票金额与申报表对比分析 `invoiceAnalysis.salesVsDeclares`
     	





### 接口数据示例

前端getReportResultById调用后端接口,响应的数据转为json格式如下(数据项未完善)

```json
{
    "basicInfo": {
        "corpName": "诸暨海鼎金属材料有限公司",
        "taxId": "91330681MA2D7C9R1B",
        "legalPerson": {}
    },
    "taxAnalysis": {
        "corporateIncomeTax": [
            {
                "year": 2022,
                "industryIncomeTaxBurden": 0.0005,
                "incomeTaxBurden": 0.0008
            },
            {
                "year": 2023,
                "industryIncomeTaxBurden": 0.0005,
                "incomeTaxBurden": 0.0008
            },
            {
                "year": 2024,
                "expenseDeductions": {
                    "rdDeduction": 0
                },
                "industryIncomeTaxBurden": 0.0006,
                "incomeAdjustments": {
                    "unrecordedScrapIncome": 0
                },
                "expenseAdjustments": {
                    "excessEntertainment": 0,
                    "nonCharitableDonations": 0,
                    "personalSocialInsurance": 0,
                    "nonDeductiblePenalties": 0
                },
                "incomeDeductions": {
                    "nonTaxableGovIncome": 0
                },
                "incomeTaxBurden": 0.0007
            },
            {
                "year": 2025,
                "expenseDeductions": {
                    "rdDeduction": 0
                },
                "industryIncomeTaxBurden": 0.0007,
                "incomeAdjustments": {
                    "unrecordedScrapIncome": 0,
                    "unrecordedAdvanceReceipts": 0
                },
                "expenseAdjustments": {
                    "excessEntertainment": 0,
                    "nonCharitableDonations": 0,
                    "personalSocialInsurance": 0,
                    "nonDeductiblePenalties": 0,
                    "uninvoicedCosts": 0
                },
                "incomeDeductions": {
                    "nonTaxableGovIncome": 0
                },
                "incomeTaxBurden": 0.0035
            }
        ],
        "vat": [
            {
                "industryVatTaxBurden": 0.0054,
                "vatBurden": 0.0179,
                "outputTax": {
                    "unrecordedScrapIncome": 0
                },
                "year": 2024,
                "addTaxBurden": 0.0032
            },
            {
                "industryVatTaxBurden": 0.0055,
                "vatBurden": 0.0179,
                "outputTax": {
                    "unrecordedScrapIncome": 0,
                    "unrecordedAdvanceReceipts": 0
                },
                "year": 2025,
                "addTaxBurden": 0.0007
            }
        ],
        "urbanMaintenanceTax": [
            {
                "year": 2022,
                "estimatedUnderpayment": 40444.343
            },
            {
                "year": 2023,
                "estimatedUnderpayment": 40444.343
            },
            {
                "year": 2024,
                "estimatedUnderpayment": 40444.343
            }
        ],
        "stampDuty": [
            {
                "year": 2024,
                "unrecordedCapitalIncrease": -50
            },
            {
                "year": 2025,
                "unrecordedCapitalIncrease": -100
            }
        ],
        "socialInsurance": [
            {
                "year": 2024
            },
            {
                "year": 2025,
                "insufficientSocialInsurance": -24088.54
            }
        ]
    },
    "productAnalysis": {
        "mainSalesProducts": [
            {
                "amount": 14843511.78,
                "quantity": 3739.7148,
                "avgPrice": 3969.16,
                "unit": "吨",
                "percentage": 43.59,
                "name": "*黑色金属冶炼压延品*开平板"
            },
            {
                "amount": 4853392.37,
                "quantity": 1531.091,
                "avgPrice": 3169.89,
                "unit": "吨",
                "percentage": 14.25,
                "name": "*黑色金属冶炼压延品*钢板"
            },
            {
                "amount": 3994102.81,
                "quantity": 905.7863,
                "avgPrice": 4409.54,
                "unit": "吨",
                "percentage": 11.73,
                "name": "*黑色金属冶炼压延品*圆钢"
            },
            {
                "amount": 1478175.6,
                "quantity": 388,
                "avgPrice": 3809.73,
                "unit": "件",
                "percentage": 4.34,
                "name": "*金属制品*机床底板"
            },
            {
                "amount": 1014442.9,
                "quantity": 162.878,
                "avgPrice": 6228.24,
                "unit": "吨",
                "percentage": 2.98,
                "name": "*黑色金属冶炼压延品*锻件"
            },
            {
                "amount": 630233.7,
                "quantity": 121540.84,
                "avgPrice": 5.19,
                "unit": "KG",
                "percentage": 1.85,
                "name": "*黑色金属冶炼压延品*无缝钢管"
            },
            {
                "amount": 427988.9,
                "quantity": 96.3938,
                "avgPrice": 4440,
                "unit": "吨",
                "percentage": 1.26,
                "name": "*黑色金属冶炼压延品*方管"
            },
            {
                "amount": 357814.5,
                "quantity": 1399,
                "avgPrice": 255.76,
                "unit": "KG",
                "percentage": 1.05,
                "name": "*金属制品*底板"
            },
            {
                "amount": 294000,
                "quantity": 67.9939,
                "avgPrice": 4323.92,
                "unit": "吨",
                "percentage": 0.86,
                "name": "*黑色金属冶炼压延品*中板"
            },
            {
                "amount": 264900,
                "quantity": 8650,
                "avgPrice": 30.62,
                "unit": "件",
                "percentage": 0.78,
                "name": "*金属制品*法兰"
            },
            {
                "amount": 238667.83,
                "quantity": 49.294,
                "avgPrice": 4841.72,
                "unit": "吨",
                "percentage": 0.7,
                "name": "*黑色金属冶炼压延品*热轧卷板"
            },
            {
                "amount": 227754.5,
                "quantity": 43.4461,
                "avgPrice": 5242.23,
                "unit": "吨",
                "percentage": 0.67,
                "name": "*黑色金属冶炼压延品*镀锌板"
            },
            {
                "amount": 217600,
                "quantity": 6656,
                "avgPrice": 32.69,
                "unit": "件",
                "percentage": 0.64,
                "name": "*黑色金属冶炼压延品*法兰"
            },
            {
                "amount": 208297.8,
                "quantity": 52.555,
                "avgPrice": 3963.42,
                "unit": "吨",
                "percentage": 0.61,
                "name": "*黑色金属冶炼压延品*槽钢"
            },
            {
                "amount": 203238.7,
                "quantity": 39.409,
                "avgPrice": 5157.16,
                "unit": "吨",
                "percentage": 0.6,
                "name": "*黑色金属冶炼压延品*无缝管"
            }
        ],
        "mainPurchaseProducts": [
            {
                "amount": 7531444.43,
                "quantity": 2045.3125,
                "avgPrice": 3682.3,
                "unit": "吨",
                "percentage": 25.95,
                "name": "*黑色金属冶炼压延品*开平板"
            },
            {
                "amount": 4610913.52,
                "quantity": 973.86395,
                "avgPrice": 4734.66,
                "unit": "吨",
                "percentage": 15.88,
                "name": "*黑色金属冶炼压延品*中厚板"
            },
            {
                "amount": 2240718.79,
                "quantity": 481.656,
                "avgPrice": 4652.11,
                "unit": "吨",
                "percentage": 7.72,
                "name": "*黑色金属冶炼压延品*圆钢"
            },
            {
                "amount": 2080140.27,
                "quantity": 543.801,
                "avgPrice": 3825.19,
                "unit": "吨",
                "percentage": 7.17,
                "name": "*黑色金属冶炼压延品*中板"
            },
            {
                "amount": 1928868.66,
                "quantity": 493.347,
                "avgPrice": 3909.76,
                "unit": "吨",
                "percentage": 6.65,
                "name": "*黑色金属冶炼压延品*钢板"
            },
            {
                "amount": 1368094.75,
                "quantity": 380.344,
                "avgPrice": 3596.99,
                "unit": "吨",
                "percentage": 4.71,
                "name": "*黑色金属冶炼压延品*热轧卷板"
            },
            {
                "amount": 1188865.17,
                "quantity": 297.575,
                "avgPrice": 3995.18,
                "unit": "吨",
                "percentage": 4.1,
                "name": "*黑色金属冶炼压延品*热薄板"
            },
            {
                "amount": 1102943.1,
                "quantity": 211.70178,
                "avgPrice": 5209.89,
                "unit": "吨",
                "percentage": 3.8,
                "name": "*黑色金属冶炼压延品*无缝钢管"
            },
            {
                "amount": 754059.6,
                "quantity": 149.215,
                "avgPrice": 5053.51,
                "unit": "吨",
                "percentage": 2.6,
                "name": "*黑色金属冶炼压延品*镀锌板"
            },
            {
                "amount": 660348.31,
                "quantity": 117.93711,
                "avgPrice": 5599.16,
                "unit": "吨",
                "percentage": 2.27,
                "name": "*黑色金属冶炼压延品*不锈钢板"
            },
            {
                "amount": 350169.34,
                "quantity": 83.163,
                "avgPrice": 4210.64,
                "unit": "吨",
                "percentage": 1.21,
                "name": "*黑色金属冶炼压延品*方管"
            },
            {
                "amount": 338996.77,
                "quantity": 85.442,
                "avgPrice": 3967.57,
                "unit": "吨",
                "percentage": 1.17,
                "name": "*黑色金属冶炼压延品*槽钢"
            },
            {
                "amount": 259397.3,
                "quantity": 59.613,
                "avgPrice": 4351.35,
                "unit": "吨",
                "percentage": 0.89,
                "name": "*黑色金属冶炼压延品*碳结板"
            },
            {
                "amount": 212678.05,
                "quantity": 39194.14,
                "avgPrice": 5.43,
                "unit": "kg",
                "percentage": 0.73,
                "name": "*黑色金属冶炼压延品*钢材"
            },
            {
                "amount": 195618.73,
                "quantity": 46.527,
                "avgPrice": 4204.41,
                "unit": "吨",
                "percentage": 0.67,
                "name": "*黑色金属冶炼压延品*优碳圆钢"
            }
        ]
    },
    "invoiceAnalysis": {
        "salesVsDeclares": [
            {
                "decl": 2.7004104E7,
                "year": 2024,
                "diff": 3128485.5,
                "sale": 3.013259E7
            },
            {
                "decl": 2.6004104E7,
                "year": 2023,
                "diff": -4184570.2,
                "sale": 2.1819534E7
            },
            {
                "decl": 2.5004104E7,
                "year": 2022,
                "diff": -2.5004104E7,
                "sale": 0
            }
        ],
        "purchaseTotalAmount": 6.7957408E7,
        "salesTotalAmount": 8.162656E7,
        "isPurchaseGTSalesTotal": false,
        "salesInvoice": {
            "invoiceRiskStats": [
                {
                    "corpName": "浙江莱梦德电力设备有限公司",
                    "totalAmount": "11644925.28",
                    "taxId": "913306815681688186"
                },
                {
                    "corpName": "浙江贝克机械有限公司",
                    "totalAmount": "6871317.15",
                    "taxId": "913306817877211181"
                },
                {
                    "corpName": "诸暨市科卫思五金配件厂",
                    "totalAmount": "6488074.91",
                    "taxId": "91330681MA2BDJJ470"
                },
                {
                    "corpName": "诸暨市中坚机械有限公司",
                    "totalAmount": "2700898.58",
                    "taxId": "91330681669188850J"
                },
                {
                    "corpName": "诸暨市科力机械有限公司",
                    "totalAmount": "2488385.70",
                    "taxId": "91330681MA2D8K9M87"
                },
                {
                    "corpName": "诸暨市华昶机械有限公司",
                    "totalAmount": "2327080.11",
                    "taxId": "91330681MA2D6K5A36"
                },
                {
                    "corpName": "浙江铜加工研究院有限公司",
                    "totalAmount": "2075487.97",
                    "taxId": "913306817258650950"
                },
                {
                    "corpName": "绍兴福天机械有限公司",
                    "totalAmount": "2011864.40",
                    "taxId": "91330681MA2D63E37T"
                },
                {
                    "corpName": "浙江数通实业有限公司",
                    "totalAmount": "1811573.70",
                    "taxId": "91330109694553135C"
                },
                {
                    "corpName": "诸暨天昊环保设备有限公司",
                    "totalAmount": "1720570.67",
                    "taxId": "91330681329997280E"
                },
                {
                    "corpName": "诸暨创盈机电科技有限公司",
                    "totalAmount": "1648879.40",
                    "taxId": "91330681MA2D6A7906"
                },
                {
                    "corpName": "绍兴市浩阳智能设备有限公司",
                    "totalAmount": "1582300.00",
                    "taxId": "91330681MADHJEFN1C"
                },
                {
                    "corpName": "绍兴国振机械有限公司",
                    "totalAmount": "1433502.30",
                    "taxId": "91330621MA2JT8WT97"
                },
                {
                    "corpName": "杭州赵振机械厂",
                    "totalAmount": "1413664.80",
                    "taxId": "91330109697084186Q"
                },
                {
                    "corpName": "绍兴国锤五金机械有限公司",
                    "totalAmount": "1280470.50",
                    "taxId": "91330621343992646H"
                }
            ]
        },
        "purchaseInvoice": {
            "nonCompliantInvoiceList": {
                "fraudulentInvoices": []
            },
            "invoiceRiskStats": [
                {
                    "corpName": "杭州鸿翼物资有限公司",
                    "totalAmount": "15109522.75",
                    "taxId": "91330105MA2J15C97E"
                },
                {
                    "corpName": "杭州恺南供应链管理有限公司",
                    "totalAmount": "9180831.24",
                    "taxId": "91330105MA2H2LPP80"
                },
                {
                    "corpName": "杭州苏宝钢铁有限公司",
                    "totalAmount": "8731709.13",
                    "taxId": "91330105589895504R"
                },
                {
                    "corpName": "浙江金投钢铁有限公司",
                    "totalAmount": "7906303.08",
                    "taxId": "91330521MA2B36DC4C"
                },
                {
                    "corpName": "杭州鑫聚源钢铁有限公司",
                    "totalAmount": "4508240.39",
                    "taxId": "91330110MA2HYJWT4L"
                },
                {
                    "corpName": "浙江乘风钢铁有限公司",
                    "totalAmount": "1916925.32",
                    "taxId": "91330105757211096N"
                },
                {
                    "corpName": "杭州常巨物资有限公司",
                    "totalAmount": "1628019.72",
                    "taxId": "91330103MA28MGB1X8"
                },
                {
                    "corpName": "杭州萧商贸易有限公司",
                    "totalAmount": "1434297.48",
                    "taxId": "91330105MA2CFFAY6T"
                },
                {
                    "corpName": "诸暨美鑫金属材料有限公司",
                    "totalAmount": "1423733.89",
                    "taxId": "91330681MA2895T214"
                },
                {
                    "corpName": "浙江江东贸易有限公司",
                    "totalAmount": "1114336.40",
                    "taxId": "91330105779299076W"
                },
                {
                    "corpName": "浙江新向远电子商务有限公司",
                    "totalAmount": "846458.08",
                    "taxId": "91330521MA29JB0W46"
                },
                {
                    "corpName": "浙江开航金属有限公司",
                    "totalAmount": "636373.60",
                    "taxId": "91330521MA2B47853L"
                },
                {
                    "corpName": "杭州旗钢物资有限公司",
                    "totalAmount": "619371.88",
                    "taxId": "91330105MA27YG4R2M"
                },
                {
                    "corpName": "杭州呈钢钢铁有限公司",
                    "totalAmount": "619248.52",
                    "taxId": "91330105MA28UEFQ8J"
                },
                {
                    "corpName": "浙江承威钢管有限公司",
                    "totalAmount": "594324.93",
                    "taxId": "91330521MA2B3XXX78"
                }
            ]
        },
        "salesTax": 9390660,
        "isOnlySales": false,
        "purchaseTax": 7777483.5,
        "taxExemptComparisons": [
            {
                "year": 2023,
                "purchase": 1.9258562E7,
                "diff": 2560971.2,
                "sale": 2.1819534E7
            },
            {
                "year": 2024,
                "purchase": 2.5706738E7,
                "diff": 4425852,
                "sale": 3.013259E7
            },
            {
                "year": 2025,
                "purchase": 1.5214627E7,
                "diff": 5069150,
                "sale": 2.0283776E7
            }
        ],
        "isSalesMLTPurchaseTax": false
    }
}
```



发票分析后端java实体类定义

```java
package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 发票分析
 *
 * <AUTHOR>
 */
@Data
public class InvoiceAnalysis {

  @ApiModelProperty("进项发票分析")
  @DataSource("进项发票")
  private InvoicePurchaseAnalysis purchaseInvoice;

  @ApiModelProperty("销项发票分析")
  @DataSource("销项发票")
  private InvoiceSalesAnalysis salesInvoice;

  @ApiModelProperty("进项发票总金额（元）")
  @DataSource("进项发票")
  private Float purchaseTotalAmount;

  @ApiModelProperty("销项发票总金额（元）")
  @DataSource("销项发票")
  private Float salesTotalAmount;

  @ApiModelProperty("进项是否大于销项")
  @DataSource("进项发票、销项发票")
  private Boolean isPurchaseGTSalesTotal;

  @ApiModelProperty("进项税额（元）")
  @DataSource("进项发票")
  private Float purchaseTax;

  @ApiModelProperty("销项税额（元）")
  @DataSource("销项发票")
  private Float salesTax;

  @ApiModelProperty("'销项税额' 远小于 '进项税额'")
  @DataSource("进项发票、销项发票")
  private Boolean isSalesMLTPurchaseTax;

  @ApiModelProperty("是否有销无进 (少进)")
  @DataSource("进项发票、销项发票")
  private Boolean isOnlySales;

  @ApiModelProperty("进项不含税金额、销项不含税金额对比分析")
  private List<TaxExemptComparison> taxExemptComparisons;

  @ApiModelProperty("发票金额与申报表对比分析")
  private List<SalesVsDeclare> salesVsDeclares;

  @ApiModelProperty("开具零税率发票列表")
  private List<Object> zeroRateInvoices;

  @ApiModelProperty("开具免税发票列表")
  private List<Object> taxExemptInvoices;

  @ApiModelProperty("大额咨询类发票明细 (人工分析)")
  private List<Object> largeConsultingInvoices;

  @ApiModelProperty("百货、超市、药店、商场等发票列表 (人工分析)")
  private List<Object> retailSupermarketInvoices;
}
```



```java
package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 进项发票分析
 *
 * <AUTHOR>
 */
@Data
public class InvoicePurchaseAnalysis {

  @ApiModelProperty("取得不合规发票抵扣进项税清单")
  private NonCompliantInvoiceList nonCompliantInvoiceList;

  @ApiModelProperty("用于不得抵扣项目未转出进项税清单")
  private List<Invoice> nonDeductibleList;

  @ApiModelProperty("发票风险统计")
  private List<InvoiceRiskStats> invoiceRiskStats;
}

```



```java
package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

/**
 * 发票数据
 *
 * <AUTHOR>
 */
@Data
public class Invoice {
  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("税号")
  private String taxId;

  @ApiModelProperty("公司名称")
  private String companyName;

  @ApiModelProperty("发票号码")
  private String invoiceNum;

  @ApiModelProperty("开票日期")
  private LocalDate invoiceDate;

  @ApiModelProperty("金额")
  private BigDecimal amount;

  @ApiModelProperty("税额")
  private BigDecimal tax;

  @ApiModelProperty("含税金额")
  private BigDecimal taxIncludedAmount;
}

```



```java
package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发票风险统计
 *
 * <AUTHOR>
 */
@Data
public class InvoiceRiskStats {

  @ApiModelProperty("企业名称")
  private String corpName;

  @ApiModelProperty("税号")
  private String taxId;

  @ApiModelProperty("总金额(元)")
  private String totalAmount;

  @ApiModelProperty("风险项(多个风险项之间用逗号分隔，包括：税务风险、是否失信被执行、法人是否限高、虚开、税案、注销)")
  private String risks;
}

```



```java
package com.qbook.insight.common.domain.report.result;

import lombok.Data;

/**
 * 销项发票分析
 *
 * <AUTHOR>
 */
@Data
public class InvoiceSalesCorp {

  private Integer year;
  private String corpName;
  private String status;
}

```



```java
package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 进项不含税金额、销项不含税金额对比分析
 *
 * <AUTHOR>
 */
@Data
public class TaxExemptComparison {

  @ApiModelProperty("年度")
  private Integer year;

  @ApiModelProperty("进项不含税金额(元)")
  @DataSource("进项发票")
  private Float purchase;

  @ApiModelProperty("销项不含税金额(元)")
  @DataSource("销项发票")
  private Float sale;

  @ApiModelProperty("销项减进项")
  private Float diff;
}

```



```java
package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发票金额与申报表对比分析
 *
 * <AUTHOR>
 */
@Data
public class SalesVsDeclare {

  @ApiModelProperty("年度")
  private Integer year;

  @ApiModelProperty("申报营业收入(元)")
  @DataSource("申报表(所得税年报、季报)")
  private Float decl;

  @ApiModelProperty("销项不含税金额(元)")
  @DataSource("销项发票")
  private Float sale;

  @ApiModelProperty("申报减开票差额(元)")
  private Float diff;
}

```





