<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';

import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import {
  createIndicatorConfig,
  getIndicatorCategoriesList,
  IndicatorApi,
  testIndicatorConfig,
  updateIndicatorConfig,
} from '#/api/indicator';

const emit = defineEmits<{
  success: [];
}>();

const formData = ref<IndicatorApi.IndicatorConfig | null>(null);
const level1Categories = ref<IndicatorApi.IndicatorCategory[]>([]);
const level2Categories = ref<IndicatorApi.IndicatorCategory[]>([]);
const level1Options = ref<{ label: string; value: number }[]>([]);
const level2Options = ref<{ label: string; value: number }[]>([]);
const categoriesLoaded = ref(false);

const testResult = ref<any>(null);
const isTestLoading = ref(false);
const testTaxId = ref('');
const testQueryParams = ref('');

// 获取分类数据
async function loadCategories() {
  if (categoriesLoaded.value) {
    return;
  }

  try {
    const [level1Response, level2Response] = await Promise.all([
      getIndicatorCategoriesList({ level: 1, status: 1 }),
      getIndicatorCategoriesList({ level: 2, status: 1 }),
    ]);

    const level1Data = Array.isArray(level1Response) ? level1Response : [];
    const level2Data = Array.isArray(level2Response) ? level2Response : [];

    level1Categories.value = level1Data;
    level2Categories.value = level2Data;

    // 构建分类选项
    level1Options.value = level1Data.map((cat) => ({
      label: cat.name,
      value: cat.id,
    }));
    level2Options.value = level2Data.map((cat) => ({
      label: cat.name,
      value: cat.id,
    }));

    // 标记已加载
    categoriesLoaded.value = true;
  } catch (error) {
    console.error('获取分类数据失败:', error);
    level1Categories.value = [];
    level2Categories.value = [];
    level1Options.value = [];
    level2Options.value = [];
  }
}

// 重新加载分类数据
function reloadCategories() {
  categoriesLoaded.value = false;
  return loadCategories();
}

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  closeOnPressEscape: false,
  draggable: true,
  onOpened: async () => {
    const data = modalApi.getData<any>();
    formData.value = data;

    await loadCategories();

    if (data && data.id) {
      // 编辑模式，设置表单值
      const formValues = {
        class1: data.class1 || undefined,
        class2: data.class2 || undefined,
        indicatorCode: data.indicatorCode || '',
        indicatorName: data.indicatorName || '',
        executeSql: data.executeSql || '',
        returnType: data.returnType || 0,
        description: data.description || '',
        status: data.status === 1,
      };

      form.setValues(formValues);
    } else {
      // 新增模式，重置表单并设置默认值
      form.resetForm();
      const formValues = {
        class1: data?.class1 || undefined,
        class2: data?.class2 || undefined,
        indicatorCode: '',
        indicatorName: '',
        executeSql: '',
        returnType: data?.returnType || undefined,
        description: '',
        status: true,
      };

      form.setValues(formValues);
    }
  },
});

/**
 * 表单配置
 */
const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择一级分类',
        allowClear: true,
        options: level1Options,
      },
      fieldName: 'class1',
      label: '一级分类',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择二级分类',
        allowClear: true,
        options: level2Options,
      },
      fieldName: 'class2',
      label: '二级分类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '指标编码',
      },
      fieldName: 'indicatorCode',
      label: '指标编码',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '指标名称',
      },
      fieldName: 'indicatorName',
      label: '指标名称',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '执行SQL语句',
        rows: 4,
      },
      fieldName: 'executeSql',
      label: '执行SQL',
      rules: 'required',
      formItemClass: 'sm:col-span-2',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择响应数据类型',
        options: IndicatorApi.RETURN_TYPE_OPTIONS,
      },
      fieldName: 'returnType',
      label: '数据类型',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '禁用',
        style: { width: '80px' },
      },
      fieldName: 'status',
      label: '状态',
      defaultValue: true,
    },

    {
      component: 'Textarea',
      componentProps: {
        placeholder: '指标描述',
        rows: 2,
      },
      fieldName: 'description',
      label: '指标描述',
      formItemClass: 'sm:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
};

const [Form, form] = useVbenForm(formOptions);

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    const validateResult = await form.validate();
    if (
      !validateResult ||
      (validateResult && !validateResult.valid) ||
      (validateResult &&
        validateResult.errors &&
        Object.keys(validateResult.errors).length > 0)
    ) {
      message.error('请填写所有必填项');
      return;
    }

    const values = await form.getValues();
    const requestData: IndicatorApi.CreateIndicatorConfigRequest = {
      class1: values.class1,
      class2: values.class2,
      indicatorCode: values.indicatorCode,
      indicatorName: values.indicatorName,
      executeSql: values.executeSql,
      returnType: values.returnType,
      description: values.description,
      status: values.status ? 1 : 0,
    };

    if (formData.value && formData.value.id) {
      // 编辑模式
      await updateIndicatorConfig({
        ...requestData,
        id: formData.value.id,
      });
      message.success('更新成功');
    } else {
      // 新增模式
      await createIndicatorConfig(requestData);
      message.success('创建成功');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('保存指标配置失败:', error);
    message.error('保存指标配置失败');
  }
}

function handleCancel() {
  modalApi.close();
}

/**
 * 执行指标测试
 */
async function handleTest() {
  try {
    // 验证表单数据
    const validateResult = await form.validate();
    if (
      !validateResult ||
      (validateResult && !validateResult.valid) ||
      (validateResult &&
        validateResult.errors &&
        Object.keys(validateResult.errors).length > 0)
    ) {
      message.error('请先完善指标配置信息');
      return;
    }

    // 获取表单值
    const values = await form.getValues();

    // 验证测试参数
    if (!testTaxId.value || !testTaxId.value.trim()) {
      message.error('请输入税号');
      return;
    }

    // 解析查询条件
    let queryParams: Record<string, any> | undefined;
    if (testQueryParams.value && testQueryParams.value.trim()) {
      try {
        queryParams = JSON.parse(testQueryParams.value);
      } catch {
        message.error('查询条件格式错误，请输入有效的JSON格式');
        return;
      }
    }

    isTestLoading.value = true;
    testResult.value = null;

    // 构建指标配置对象
    const indicatorConfig: IndicatorApi.IndicatorConfig = {
      id: formData.value?.id || 0,
      class1: values.class1,
      class2: values.class2,
      indicatorCode: values.indicatorCode,
      indicatorName: values.indicatorName,
      executeSql: values.executeSql,
      returnType: values.returnType,
      description: values.description,
      status: values.status ? 1 : 0,
      createdAt: formData.value?.createdAt || '',
      updatedAt: formData.value?.updatedAt || '',
    };

    const response = await testIndicatorConfig({
      indicatorConfig,
      taxId: testTaxId.value,
      queryParams,
    });

    testResult.value = response;
    message.success('测试执行成功');
  } catch (error: any) {
    console.error('指标测试失败:', error);
    const errorMessage =
      error?.response?.data?.message || error?.message || '测试执行失败';
    testResult.value = {
      error: true,
      message: errorMessage,
    };
  } finally {
    isTestLoading.value = false;
  }
}

/**
 * 格式化测试结果显示
 */
function formatTestResult(result: any): string {
  if (!result) return '';

  if (result.error) {
    return `错误: ${result.message}`;
  }

  try {
    return JSON.stringify(result, null, 2);
  } catch {
    return String(result);
  }
}

defineExpose({
  reloadCategories,
});
</script>

<template>
  <Modal
    :title="formData && formData.id ? '编辑指标配置' : '新增指标配置'"
    :width="900"
  >
    <div class="space-y-4">
      <Form />
      <div class="border-t pt-4">
        <h4 class="mb-4 text-sm font-medium text-gray-900">测试参数</h4>
        <div class="space-y-4">
          <div>
            <label class="mb-1 block text-sm font-medium text-gray-700">
              <span class="text-red-500">*</span> 税号
            </label>
            <input
              v-model="testTaxId"
              type="text"
              placeholder="请输入税号"
              class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label class="mb-1 block text-sm font-medium text-gray-700">
              查询条件
              <span class="ml-1 text-xs text-gray-400">(?)</span>
            </label>
            <textarea
              v-model="testQueryParams"
              rows="4"
              placeholder="请输入查询条件(JSON格式)"
              class="w-full resize-none rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>
          <div class="flex justify-start">
            <Button type="primary" :loading="isTestLoading" @click="handleTest">
              执行测试
            </Button>
          </div>
          <div v-if="testResult !== null" class="rounded bg-gray-50 p-3">
            <h5 class="mb-2 text-sm font-medium text-gray-700">测试结果:</h5>
            <pre
              class="max-h-60 overflow-y-auto whitespace-pre-wrap rounded border bg-white p-2 font-mono text-xs"
              :class="
                testResult.error
                  ? 'border-red-200 text-red-600'
                  : 'border-gray-200 text-gray-800'
              "
              >{{ formatTestResult(testResult) }}
            </pre>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="handleCancel"> 取消 </Button>
        <Button type="primary" @click="handleSubmit"> 保存 </Button>
      </div>
    </template>
  </Modal>
</template>
